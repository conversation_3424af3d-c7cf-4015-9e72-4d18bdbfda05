"""
PTS Directory Management Service

This module implements the directory creation functionality based on the desktop version.
It handles copying folder contents, excluding other PTS files, and managing file conflicts.
"""

import shutil
from pathlib import Path
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass

from loguru import logger
from backend.pts_renamer.models.pts_rename_entities import PTSRenameResult


@dataclass
class DirectoryPreview:
    """Preview result for directory creation"""
    original_name: str
    directory_name: str
    will_create: bool
    error_message: Optional[str] = None


class PTSDirectoryManager:
    """
    PTS Directory Manager
    
    Implements the exact directory creation logic from the desktop version:
    1. Check filename vs folder name conflicts
    2. Copy entire folder contents to new directory
    3. Remove other PTS files (keep only target file)
    4. Remove INI files from copied directory
    5. Handle file conflicts and permissions
    """
    
    def __init__(self):
        self.logger = logger
    
    def create_pts_directory(self, pts_file: Path, original_folder: Path) -> Tuple[bool, str]:
        """
        Create directory structure for PTS file (exact logic from desktop version)
        
        Args:
            pts_file: Path to the PTS file
            original_folder: Path to the original folder containing the PTS file
            
        Returns:
            Tuple of (success, directory_name_or_error_message)
        """
        try:
            # Use filename (without extension) as directory name
            dir_name = pts_file.stem
            
            # Check if PTS filename matches original folder name
            original_folder_name = original_folder.name
            if dir_name == original_folder_name:
                return False, f"PTS檔案名稱 '{dir_name}' 與文件夾名稱 '{original_folder_name}' 相同，無需創建目錄"
            
            # Target directory in parent of original folder
            parent_dir = original_folder.parent
            dir_path = parent_dir / dir_name
            
            # Check if original folder exists
            if not original_folder.exists():
                return False, f"原始文件夾不存在: {original_folder}"
            
            # If directory already exists, delete it first
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.logger.info(f"Removed existing directory: {dir_path}")
                except Exception as e:
                    return False, f"刪除現有目錄失敗: {str(e)}"
            
            # Copy entire original folder to parent directory
            try:
                shutil.copytree(original_folder, dir_path)
                self.logger.info(f"Copied folder from {original_folder} to {dir_path}")
            except Exception as e:
                return False, f"複製文件夾失敗: {str(e)}"
            
            # Remove other PTS files (keep only the target PTS file)
            success = self.copy_folder_contents(original_folder, dir_path, pts_file.name)
            if not success:
                return False, "處理文件夾內容時發生錯誤"
            
            # If current PTS file is not in the created directory, copy it
            target_pts_file = dir_path / pts_file.name
            if not target_pts_file.exists():
                try:
                    shutil.copy2(pts_file, target_pts_file)
                    self.logger.info(f"Copied PTS file to directory: {target_pts_file}")
                except Exception as e:
                    return False, f"複製當前PTS檔案失敗: {str(e)}"
            
            self.logger.info(f"Successfully created directory: {dir_name}")
            return True, dir_name
            
        except Exception as e:
            error_msg = f"創建目錄時發生未知錯誤: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def copy_folder_contents(self, source: Path, target: Path, pts_filename: str) -> bool:
        """
        Copy folder contents excluding other PTS files (exact logic from desktop version)
        
        Args:
            source: Source folder path
            target: Target folder path
            pts_filename: Name of the PTS file to keep
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove other PTS files (keep only the specified one)
            pts_files_to_delete = []
            for pts_file_in_dir in target.glob("*.pts"):
                if pts_file_in_dir.name != pts_filename:
                    pts_files_to_delete.append(pts_file_in_dir)
            
            for pts_file_to_delete in pts_files_to_delete:
                try:
                    pts_file_to_delete.unlink()
                    self.logger.info(f"Removed other PTS file: {pts_file_to_delete.name}")
                except Exception as e:
                    self.logger.error(f"Failed to remove PTS file {pts_file_to_delete.name}: {str(e)}")
                    return False
            
            # Remove all *.ini files
            ini_files_to_delete = []
            for ini_file_in_dir in target.glob("*.ini"):
                ini_files_to_delete.append(ini_file_in_dir)
            
            for ini_file_to_delete in ini_files_to_delete:
                try:
                    ini_file_to_delete.unlink()
                    self.logger.info(f"Removed INI file: {ini_file_to_delete.name}")
                except Exception as e:
                    self.logger.error(f"Failed to remove INI file {ini_file_to_delete.name}: {str(e)}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing folder contents: {str(e)}")
            return False
    
    def preview_directory_creation(self, files: List[Path]) -> List[DirectoryPreview]:
        """
        Preview directory creation operations
        
        Args:
            files: List of PTS files to preview
            
        Returns:
            List of directory creation previews
        """
        previews = []
        
        for file_path in files:
            original_name = file_path.name
            dir_name = file_path.stem
            original_folder = file_path.parent
            original_folder_name = original_folder.name
            
            # Check if filename matches folder name
            if dir_name == original_folder_name:
                previews.append(DirectoryPreview(
                    original_name=original_name,
                    directory_name=dir_name,
                    will_create=False,
                    error_message=f"PTS檔案名稱 '{dir_name}' 與文件夾名稱 '{original_folder_name}' 相同，無需創建目錄"
                ))
            else:
                # Check if target directory would exist
                parent_dir = original_folder.parent
                dir_path = parent_dir / dir_name
                
                if dir_path.exists():
                    previews.append(DirectoryPreview(
                        original_name=original_name,
                        directory_name=dir_name,
                        will_create=True,
                        error_message=f"將覆蓋現有目錄: {dir_name}"
                    ))
                else:
                    previews.append(DirectoryPreview(
                        original_name=original_name,
                        directory_name=dir_name,
                        will_create=True
                    ))
        
        return previews
    
    def batch_create_directories(self, files: List[Path]) -> List[PTSRenameResult]:
        """
        Batch create directories for multiple PTS files
        
        Args:
            files: List of PTS files to process
            
        Returns:
            List of directory creation results
        """
        results = []
        
        # Group files by their parent folder
        folder_files_map = {}
        for file_path in files:
            folder = file_path.parent
            if folder not in folder_files_map:
                folder_files_map[folder] = []
            folder_files_map[folder].append(file_path)
        
        # Process each file
        for file_path in files:
            original_folder = file_path.parent
            success, dir_name_or_error = self.create_pts_directory(file_path, original_folder)
            
            result = PTSRenameResult(
                original_name=file_path.name,
                new_name=f"目錄: {dir_name_or_error}" if success else "",
                operation="directory_creation",
                success=success,
                error_message=None if success else dir_name_or_error
            )
            results.append(result)
        
        return results
    
    def validate_directory_creation(self, pts_file: Path, original_folder: Path) -> Tuple[bool, str]:
        """
        Validate if directory can be created for PTS file
        
        Args:
            pts_file: Path to the PTS file
            original_folder: Path to the original folder
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not pts_file.exists():
            return False, "PTS檔案不存在"
        
        if not original_folder.exists():
            return False, "原始文件夾不存在"
        
        if not original_folder.is_dir():
            return False, "原始路徑不是文件夾"
        
        # Check if filename matches folder name
        dir_name = pts_file.stem
        original_folder_name = original_folder.name
        if dir_name == original_folder_name:
            return False, f"PTS檔案名稱 '{dir_name}' 與文件夾名稱 '{original_folder_name}' 相同，無需創建目錄"
        
        # Check parent directory permissions
        parent_dir = original_folder.parent
        if not parent_dir.exists():
            return False, "父目錄不存在"
        
        try:
            # Test write permissions by creating a temporary file
            test_file = parent_dir / f"test_write_{dir_name}.tmp"
            test_file.touch()
            test_file.unlink()
        except Exception as e:
            return False, f"沒有寫入權限: {str(e)}"
        
        return True, ""
    
    def cleanup_empty_directories(self, base_path: Path) -> int:
        """
        Clean up empty directories created during processing
        
        Args:
            base_path: Base path to search for empty directories
            
        Returns:
            Number of directories cleaned up
        """
        cleaned_count = 0
        
        try:
            for dir_path in base_path.iterdir():
                if dir_path.is_dir():
                    try:
                        # Check if directory is empty
                        if not any(dir_path.iterdir()):
                            dir_path.rmdir()
                            self.logger.info(f"Cleaned up empty directory: {dir_path}")
                            cleaned_count += 1
                    except Exception as e:
                        self.logger.warning(f"Could not clean up directory {dir_path}: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"Error during directory cleanup: {str(e)}")
        
        return cleaned_count