"""
PTS Renamer Repository Interface and Implementation

This module defines the repository interface for PTS Renamer data access
following hexagonal architecture principles. It provides abstraction over
data persistence and integrates with the existing outlook.db database.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..models.pts_rename_entities import (
    PTSProcessingJob, 
    PTSFile, 
    PTSRenameResult,
    PTSQCFile,
    PTSDirectory
)


class IPTSRenameRepository(ABC):
    """
    Repository interface for PTS Renamer data access
    
    This interface defines the contract for data persistence operations
    following the repository pattern from hexagonal architecture.
    """
    
    @abstractmethod
    async def save_job(self, job: PTSProcessingJob) -> str:
        """
        Save a PTS processing job
        
        Args:
            job: The processing job to save
            
        Returns:
            The job ID
            
        Raises:
            RepositoryError: If save operation fails
        """
        pass
    
    @abstractmethod
    async def get_job(self, job_id: str) -> Optional[PTSProcessingJob]:
        """
        Retrieve a PTS processing job by ID
        
        Args:
            job_id: The job identifier
            
        Returns:
            The processing job if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def update_job_status(self, job_id: str, status: str, 
                               error_message: Optional[str] = None) -> bool:
        """
        Update job status
        
        Args:
            job_id: The job identifier
            status: New status value
            error_message: Optional error message
            
        Returns:
            True if update successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def update_job_progress(self, job_id: str, files_processed: int, 
                                 progress_percentage: int) -> bool:
        """
        Update job progress
        
        Args:
            job_id: The job identifier
            files_processed: Number of files processed
            progress_percentage: Progress percentage (0-100)
            
        Returns:
            True if update successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def add_job_result(self, job_id: str, result: PTSRenameResult) -> bool:
        """
        Add a processing result to a job
        
        Args:
            job_id: The job identifier
            result: The processing result to add
            
        Returns:
            True if add successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_pts_files(self, upload_id: str) -> List[PTSFile]:
        """
        Get PTS files for an upload
        
        Args:
            upload_id: The upload identifier
            
        Returns:
            List of PTS files
        """
        pass
    
    @abstractmethod
    async def save_pts_file(self, pts_file: PTSFile) -> bool:
        """
        Save a PTS file record
        
        Args:
            pts_file: The PTS file to save
            
        Returns:
            True if save successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_job_results(self, job_id: str) -> List[PTSRenameResult]:
        """
        Get all results for a job
        
        Args:
            job_id: The job identifier
            
        Returns:
            List of processing results
        """
        pass
    
    @abstractmethod
    async def get_jobs_by_status(self, status: str, limit: Optional[int] = None) -> List[PTSProcessingJob]:
        """
        Get jobs by status
        
        Args:
            status: Job status to filter by
            limit: Maximum number of jobs to return
            
        Returns:
            List of jobs with the specified status
        """
        pass
    
    @abstractmethod
    async def get_jobs_by_upload_id(self, upload_id: str) -> List[PTSProcessingJob]:
        """
        Get jobs for a specific upload
        
        Args:
            upload_id: The upload identifier
            
        Returns:
            List of jobs for the upload
        """
        pass
    
    @abstractmethod
    async def delete_expired_jobs(self, expiry_hours: int = 24) -> int:
        """
        Delete expired jobs and their associated data
        
        Args:
            expiry_hours: Hours after which jobs are considered expired
            
        Returns:
            Number of jobs deleted
        """
        pass
    
    @abstractmethod
    async def get_job_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        Get job processing statistics
        
        Args:
            days: Number of days to include in statistics
            
        Returns:
            Dictionary containing statistics
        """
        pass
    
    @abstractmethod
    async def save_qc_file(self, qc_file: PTSQCFile) -> bool:
        """
        Save QC file record
        
        Args:
            qc_file: The QC file to save
            
        Returns:
            True if save successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def save_directory(self, directory: PTSDirectory) -> bool:
        """
        Save directory record
        
        Args:
            directory: The directory to save
            
        Returns:
            True if save successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_qc_files_for_job(self, job_id: str) -> List[PTSQCFile]:
        """
        Get QC files for a job
        
        Args:
            job_id: The job identifier
            
        Returns:
            List of QC files
        """
        pass
    
    @abstractmethod
    async def get_directories_for_job(self, job_id: str) -> List[PTSDirectory]:
        """
        Get directories for a job
        
        Args:
            job_id: The job identifier
            
        Returns:
            List of directories
        """
        pass
    
    @abstractmethod
    async def save_upload_record(self, upload_record: Dict[str, Any]) -> bool:
        """
        Save upload record
        
        Args:
            upload_record: Upload record data
            
        Returns:
            True if save successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_upload_record(self, upload_id: str) -> Optional[Dict[str, Any]]:
        """
        Get upload record by ID
        
        Args:
            upload_id: Upload identifier
            
        Returns:
            Upload record if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def update_job_compression_info(self, job_id: str, compression_info: Dict[str, Any]) -> bool:
        """
        Update job compression information
        
        Args:
            job_id: Job identifier
            compression_info: Compression information
            
        Returns:
            True if update successful, False otherwise
        """
        pass


class RepositoryError(Exception):
    """Exception raised by repository operations"""
    
    def __init__(self, message: str, operation: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.operation = operation
        self.details = details or {}
        self.timestamp = datetime.now()


class PTSRenameRepositoryConfig:
    """Configuration for PTS Renamer repository"""
    
    def __init__(self,
                 database_path: str = "outlook.db",
                 connection_timeout: int = 30,
                 enable_wal_mode: bool = True,
                 enable_foreign_keys: bool = True,
                 max_connections: int = 10):
        self.database_path = database_path
        self.connection_timeout = connection_timeout
        self.enable_wal_mode = enable_wal_mode
        self.enable_foreign_keys = enable_foreign_keys
        self.max_connections = max_connections