# This file is a part of Dramatiq.
#
# <AUTHOR> <EMAIL>
#
# Dramatiq is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation, either version 3 of the License, or (at
# your option) any later version.
#
# Dramatiq is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
# FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
# License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

from ..errors import DramatiqError


class ResultError(DramatiqError):
    """Base class for result errors.
    """


class ResultTimeout(ResultError):
    """Raised when waiting for a result times out.
    """


class ResultMissing(ResultError):
    """Raised when a result can't be found.
    """


class ResultFailure(ResultError):
    """Raised when getting a result from an actor that failed.
    """

    def __init__(self, message="", orig_exc_type="", orig_exc_msg=""):
        super().__init__(message)

        self.orig_exc_type = orig_exc_type
        self.orig_exc_msg = orig_exc_msg
