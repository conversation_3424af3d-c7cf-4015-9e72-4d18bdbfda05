# Epic-01 Database Infrastructure - Deployment Guide

## 📋 目錄

1. [概述](#概述)
2. [緊急聯絡資訊](#緊急聯絡資訊)
3. [備份策略](#備份策略)
4. [遷移部署程序](#遷移部署程序)
5. [災難恢復程序](#災難恢復程序)
6. [故障排除](#故障排除)
7. [監控和告警](#監控和告警)
8. [測試驗證](#測試驗證)

---

## 📖 概述

本文檔為 Epic-01 Database Infrastructure 的災難恢復手冊，涵蓋資料庫遷移、備份恢復、故障排除等關鍵操作程序。

### 🎯 RTO/RPO 目標

- **RTO (Recovery Time Objective)**: 30 分鐘
- **RPO (Recovery Point Objective)**: 1 小時
- **可用性目標**: 99.9%

### 📊 系統關鍵指標

- **資料庫大小**: 通常 < 1GB
- **每日增長**: 約 10-50MB
- **並發連接數**: 5-20
- **備份頻率**: 每日自動，每週手動

---

## 📞 緊急聯絡資訊

### 值班人員 (24/7)

| 角色 | 姓名 | 電話 | Email |
|------|------|------|-------|
| 主要 DBA | 資料庫管理員 | +886-xxx-xxxx | <EMAIL> |
| 備用 DBA | 系統工程師 | +886-xxx-xxxx | <EMAIL> |
| 系統管理員 | DevOps 工程師 | +886-xxx-xxxx | <EMAIL> |

### 升級路徑

1. **P1 (立即)**: 系統完全不可用 → 立即通知主要 DBA
2. **P2 (1小時內)**: 性能嚴重降低 → 通知備用 DBA
3. **P3 (4小時內)**: 功能部分異常 → 工作時間處理

---

## 💾 備份策略

### 自動化備份

```bash
# 每日備份 (保留 7 天)
0 2 * * * /usr/local/bin/python3 scripts/database_backup_automation.py --type daily

# 每週備份 (保留 4 週)
0 3 * * 0 /usr/local/bin/python3 scripts/database_backup_automation.py --type weekly

# 每月備份 (保留 12 個月)
0 4 1 * * /usr/local/bin/python3 scripts/database_backup_automation.py --type monthly
```

### 備份驗證

每個備份檔案都包含：
- **完整性檢查**: SQLite PRAGMA integrity_check
- **SHA256 校驗**: 檔案完整性驗證
- **結構驗證**: 關鍵表和欄位檢查

### 備份存儲位置

```
backups/
├── automated/
│   ├── daily/          # 7 天保留
│   ├── weekly/         # 4 週保留
│   └── monthly/        # 12 個月保留
├── manual/             # 手動備份
└── migrations/         # 遷移前備份
```

---

## 🚀 遷移部署程序

### 部署前檢查清單

- [ ] **備份確認**: 最新備份已創建且驗證通過
- [ ] **環境檢查**: 開發/測試環境遷移成功
- [ ] **依賴確認**: Python 套件和系統依賴已更新
- [ ] **權限檢查**: 資料庫檔案讀寫權限正確
- [ ] **維護窗口**: 已安排並通知相關人員
- [ ] **回滾計劃**: 回滾程序已準備並測試

### 標準部署流程

#### 步驟 1: 預備作業

```bash
# 1. 停止應用服務
sudo systemctl stop outlook-summary

# 2. 創建部署前備份
python scripts/database_backup_automation.py --type manual

# 3. 驗證遷移腳本
python scripts/validate_epic01_migrations.py --mode full
```

#### 步驟 2: 執行遷移

```bash
# 1. 初始化 Alembic (首次)
alembic init alembic

# 2. 執行遷移
alembic upgrade head

# 3. 驗證遷移結果
python scripts/validate_epic01_migrations.py --mode rollback
```

#### 步驟 3: 驗證和啟動

```bash
# 1. 資料庫完整性檢查
sqlite3 data/email_inbox.db "PRAGMA integrity_check;"

# 2. 應用層測試
python -m pytest tests/integration/test_database_integration.py

# 3. 重啟服務
sudo systemctl start outlook-summary

# 4. 健康檢查
curl -f http://localhost:5000/health || echo "Health check failed"
```

### 回滾程序

如果遷移失敗，立即執行回滾：

```bash
# 方法 1: Alembic 回滾 (推薦)
alembic downgrade <previous_revision>

# 方法 2: 備份恢復 (最後手段)
cp backups/migrations/latest_backup.db data/email_inbox.db
sudo systemctl restart outlook-summary
```

---

## 🆘 災難恢復程序

### 場景 1: 資料庫檔案損毀

**症狀**: SQLite database disk image is malformed

**恢復步驟**:

```bash
# 1. 立即停止服務
sudo systemctl stop outlook-summary

# 2. 檢查檔案完整性
sqlite3 data/email_inbox.db "PRAGMA integrity_check;"

# 3. 嘗試修復
sqlite3 data/email_inbox.db ".recover" | sqlite3 data/email_inbox_recovered.db

# 4. 如修復失敗，使用最新備份
cp backups/automated/daily/latest_backup.db data/email_inbox.db

# 5. 重啟服務
sudo systemctl start outlook-summary
```

### 場景 2: 遷移失敗

**症狀**: 遷移過程中出現錯誤，資料不一致

**恢復步驟**:

```bash
# 1. 立即停止服務
sudo systemctl stop outlook-summary

# 2. 檢查 Alembic 狀態
alembic current

# 3. 嘗試回滾到安全版本
alembic downgrade base

# 4. 恢復遷移前備份
cp backups/migrations/pre_migration_backup.db data/email_inbox.db

# 5. 重啟服務並驗證
sudo systemctl start outlook-summary
python scripts/validate_epic01_migrations.py --mode syntax
```

### 場景 3: 資料遺失

**症狀**: 關鍵資料表為空或記錄數量異常減少

**恢復步驟**:

```bash
# 1. 立即停止服務
sudo systemctl stop outlook-summary

# 2. 分析資料遺失範圍
sqlite3 data/email_inbox.db <<EOF
SELECT COUNT(*) as email_count FROM emails;
SELECT COUNT(*) as attachment_count FROM attachments;
SELECT COUNT(*) as status_count FROM email_download_status;
EOF

# 3. 使用最近可用備份
python scripts/database_backup_automation.py --status
cp backups/automated/daily/latest_verified_backup.db data/email_inbox.db

# 4. 驗證恢復的資料
python scripts/validate_epic01_migrations.py --mode full

# 5. 重啟服務
sudo systemctl start outlook-summary
```

---

## 🔧 故障排除

### 常見問題和解決方案

#### 問題 1: "database is locked" 錯誤

**原因**: 多個程序同時訪問資料庫

**解決方案**:
```bash
# 1. 檢查佔用程序
lsof data/email_inbox.db

# 2. 優雅停止相關服務
sudo systemctl stop outlook-summary

# 3. 強制終止佔用程序 (謹慎使用)
sudo pkill -f "python.*email"

# 4. 重啟服務
sudo systemctl start outlook-summary
```

#### 問題 2: 新欄位不存在錯誤

**原因**: 遷移未完全執行或回滾不完整

**解決方案**:
```bash
# 1. 檢查當前 schema 版本
alembic current

# 2. 檢查表結構
sqlite3 data/email_inbox.db ".schema emails"

# 3. 重新執行遷移
alembic upgrade head

# 4. 驗證結果
python scripts/validate_epic01_migrations.py --mode syntax
```

#### 問題 3: 索引不存在錯誤

**原因**: 部分遷移腳本執行失敗

**解決方案**:
```bash
# 1. 檢查現有索引
sqlite3 data/email_inbox.db ".indices emails"

# 2. 手動創建缺失索引
sqlite3 data/email_inbox.db <<EOF
CREATE INDEX IF NOT EXISTS idx_email_download_success ON emails (download_success);
CREATE INDEX IF NOT EXISTS idx_email_processing_success ON emails (processing_success);
EOF

# 3. 驗證索引
sqlite3 data/email_inbox.db "EXPLAIN QUERY PLAN SELECT * FROM emails WHERE download_success = 1;"
```

### 性能問題診斷

#### 慢查詢分析

```sql
-- 啟用查詢分析
.timer on

-- 檢查大表查詢
SELECT COUNT(*) FROM emails;
SELECT COUNT(*) FROM email_download_retry_log;

-- 分析索引使用
EXPLAIN QUERY PLAN SELECT * FROM emails WHERE download_success = 1;
```

#### 資料庫統計更新

```bash
# 更新統計資訊
sqlite3 data/email_inbox.db "ANALYZE;"

# 檢查資料庫大小
sqlite3 data/email_inbox.db "PRAGMA page_count; PRAGMA page_size;"
```

---

## 📊 監控和告警

### 健康檢查腳本

```bash
#!/bin/bash
# database_health_monitor.sh

# 基本連接測試
if ! sqlite3 data/email_inbox.db "SELECT 1;" > /dev/null 2>&1; then
    echo "CRITICAL: Database connection failed"
    exit 2
fi

# 完整性檢查
if ! sqlite3 data/email_inbox.db "PRAGMA integrity_check;" | grep -q "ok"; then
    echo "CRITICAL: Database integrity check failed"
    exit 2
fi

# 檢查關鍵表
tables=("emails" "email_download_status" "email_download_retry_log")
for table in "${tables[@]}"; do
    if ! sqlite3 data/email_inbox.db "SELECT COUNT(*) FROM $table;" > /dev/null 2>&1; then
        echo "WARNING: Table $table not accessible"
        exit 1
    fi
done

echo "OK: Database health check passed"
exit 0
```

### 告警閾值

| 指標 | 警告閾值 | 緊急閾值 | 檢查頻率 |
|------|----------|----------|----------|
| 資料庫大小 | > 800MB | > 1GB | 每小時 |
| 連接失敗率 | > 5% | > 10% | 每 5 分鐘 |
| 查詢響應時間 | > 1秒 | > 5秒 | 每分鐘 |
| 備份失敗 | 1次 | 2次連續 | 每次備份後 |

### 監控命令

```bash
# 即時監控
watch -n 5 'sqlite3 data/email_inbox.db "SELECT COUNT(*) as total_emails FROM emails; SELECT COUNT(*) as pending_downloads FROM email_download_status WHERE status=\"pending\";"'

# 性能監控
python scripts/database_health_monitor.py --mode continuous --interval 60
```

---

## ✅ 測試驗證

### 遷移測試

```bash
# 1. 完整驗證套件
python scripts/validate_epic01_migrations.py --mode full

# 2. 回滾測試
python scripts/validate_epic01_migrations.py --mode rollback

# 3. 性能測試
python scripts/validate_epic01_migrations.py --mode performance
```

### 功能測試

```bash
# 1. 資料庫操作測試
python -m pytest tests/integration/test_database_integration.py -v

# 2. API 端點測試
python -m pytest tests/api/test_api_endpoints.py -v

# 3. 端對端測試
python -m pytest tests/e2e/test_database_field_display_e2e.py -v
```

### 負載測試

```bash
# 並發連接測試
python tests/load/test_multi_user_production.py

# 大量資料測試
python tests/performance/test_integration_benchmarks.py
```

---

## 📈 持續改進

### 定期檢查項目

#### 每週檢查
- [ ] 備份狀態和完整性
- [ ] 資料庫大小增長趨勢
- [ ] 慢查詢日誌分析
- [ ] 系統資源使用情況

#### 每月檢查
- [ ] 災難恢復演練
- [ ] 備份恢復測試
- [ ] 性能基準測試
- [ ] 文檔更新需求

#### 每季檢查
- [ ] RTO/RPO 目標達成情況
- [ ] 備份策略調整
- [ ] 監控告警優化
- [ ] 技術債務評估

### 改進建議

1. **自動化增強**
   - 實施更細粒度的監控
   - 自動化故障恢復程序
   - 智能告警過濾

2. **備份優化**
   - 增量備份實施
   - 異地備份策略
   - 備份壓縮和加密

3. **性能優化**
   - 查詢性能調優
   - 索引策略優化
   - 連接池配置

---

## 📝 變更記錄

| 版本 | 日期 | 變更內容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-08-20 | 初始版本，Epic-01 遷移支援 | Database Admin |

---

## 📚 相關文檔

- [Epic-01 Database Infrastructure](docs/epics/epic-01-database-infrastructure.md)
- [Database Migration Guide](scripts/validate_epic01_migrations.py)
- [Backup Automation](scripts/database_backup_automation.py)
- [Health Monitoring](scripts/database_health_monitor.py)

---

**⚠️ 重要提醒**: 本文檔包含生產環境的關鍵操作程序，請定期更新並確保團隊成員熟悉相關流程。在執行任何生產環境操作前，請務必創建備份並在測試環境中驗證。