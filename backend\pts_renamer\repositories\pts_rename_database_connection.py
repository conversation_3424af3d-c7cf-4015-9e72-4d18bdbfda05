"""
PTS Renamer Database Connection

Simple database connection class following the existing patterns
in the shared infrastructure.
"""

import sqlite3
import threading
from pathlib import Path
from typing import Optional
from contextlib import contextmanager


class PTSRenameDatabaseConnection:
    """
    Simple database connection manager for PTS Renamer
    
    Follows the pattern established in TaskStatusDB for consistency
    with the existing infrastructure.
    """
    
    def __init__(self, db_path: str = "data/outlook.db"):
        """
        Initialize database connection
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self._local = threading.local()
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """Ensure database directory exists"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            
            # Enable foreign keys
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            
        return self._local.connection
    
    @contextmanager
    def get_session(self):
        """
        Get database session context manager
        
        Provides a simple session-like interface compatible with
        the repository pattern while using SQLite directly.
        """
        conn = self._get_connection()
        try:
            yield PTSRenameDatabaseSession(conn)
        except Exception:
            conn.rollback()
            raise
        else:
            conn.commit()
    
    def close(self):
        """Close database connection"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


class PTSRenameDatabaseSession:
    """
    Simple database session wrapper
    
    Provides basic query, add, and commit methods to maintain
    compatibility with the repository interface.
    """
    
    def __init__(self, connection: sqlite3.Connection):
        self.connection = connection
        self._objects_to_add = []
    
    def query(self, model_class):
        """Create a simple query builder"""
        return PTSRenameQuery(self.connection, model_class)
    
    def add(self, obj):
        """Add object to session (will be saved on commit)"""
        self._objects_to_add.append(obj)
    
    def commit(self):
        """Commit all pending changes"""
        # Save any added objects
        for obj in self._objects_to_add:
            obj.save(self.connection)
        self._objects_to_add.clear()
        
        # Commit the transaction
        self.connection.commit()
    
    def rollback(self):
        """Rollback transaction"""
        self.connection.rollback()
        self._objects_to_add.clear()
    
    def flush(self):
        """Flush pending changes (no-op for SQLite)"""
        pass
    
    def delete(self, obj):
        """Delete object from database"""
        obj.delete(self.connection)


class PTSRenameQuery:
    """
    Simple query builder for database models
    
    Provides basic filtering and retrieval methods.
    """
    
    def __init__(self, connection: sqlite3.Connection, model_class):
        self.connection = connection
        self.model_class = model_class
        self._filters = []
        self._joins = []
        self._order_by = []
        self._limit_value = None
    
    def filter(self, *conditions):
        """Add filter conditions"""
        # For simplicity, we'll handle this in the specific model implementations
        new_query = PTSRenameQuery(self.connection, self.model_class)
        new_query._filters = self._filters + list(conditions)
        new_query._joins = self._joins.copy()
        new_query._order_by = self._order_by.copy()
        new_query._limit_value = self._limit_value
        return new_query
    
    def join(self, other_model):
        """Add join to query"""
        new_query = PTSRenameQuery(self.connection, self.model_class)
        new_query._filters = self._filters.copy()
        new_query._joins = self._joins + [other_model]
        new_query._order_by = self._order_by.copy()
        new_query._limit_value = self._limit_value
        return new_query
    
    def order_by(self, *columns):
        """Add ordering to query"""
        new_query = PTSRenameQuery(self.connection, self.model_class)
        new_query._filters = self._filters.copy()
        new_query._joins = self._joins.copy()
        new_query._order_by = self._order_by + list(columns)
        new_query._limit_value = self._limit_value
        return new_query
    
    def limit(self, count):
        """Add limit to query"""
        new_query = PTSRenameQuery(self.connection, self.model_class)
        new_query._filters = self._filters.copy()
        new_query._joins = self._joins.copy()
        new_query._order_by = self._order_by.copy()
        new_query._limit_value = count
        return new_query
    
    def first(self):
        """Get first result"""
        results = self.all()
        return results[0] if results else None
    
    def all(self):
        """Get all results"""
        # Delegate to model class for actual query execution
        return self.model_class.query_with_filters(
            self.connection, 
            self._filters, 
            self._joins, 
            self._order_by, 
            self._limit_value
        )
    
    def count(self):
        """Get count of results"""
        return len(self.all())


# ============================================================================
# 🔧 Factory Functions
# ============================================================================

def get_pts_database_connection(db_path: str = "outlook.db") -> PTSRenameDatabaseConnection:
    """
    Factory function to get PTS database connection
    
    Args:
        db_path: Path to the database file
        
    Returns:
        PTSRenameDatabaseConnection: Database connection instance
    """
    return PTSRenameDatabaseConnection(db_path)


def get_pts_database_session(db_path: str = "outlook.db") -> 'PTSRenameSession':
    """
    Factory function to get PTS database session
    
    Args:
        db_path: Path to the database file
        
    Returns:
        PTSRenameSession: Database session instance
    """
    connection = get_pts_database_connection(db_path)
    return connection.get_session()