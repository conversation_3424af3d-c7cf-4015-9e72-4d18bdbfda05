"""
PTS File Renaming Processor

This module implements the core PTS file renaming functionality based on the desktop version.
It supports pattern matching with regex and placeholder substitution ({old}, {ext}, {num}).
"""

import re
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

from loguru import logger
from backend.pts_renamer.models.pts_rename_entities import PTSFile, PTSRenameResult


@dataclass
class RenamePreview:
    """Preview result for file renaming operation"""
    original_name: str
    new_name: str
    will_rename: bool
    error_message: Optional[str] = None


class PTSRenameProcessor:
    """
    PTS File Renaming Processor
    
    Implements the exact logic from the desktop version for renaming PTS files
    with support for regex patterns and placeholder substitution.
    """
    
    def __init__(self):
        self.logger = logger
    
    def rename_file(self, file_path: Path, old_pattern: str, new_pattern: str) -> Tuple[bool, str, str]:
        """
        Rename PTS file based on pattern (exact logic from desktop version)
        
        Args:
            file_path: Path to the PTS file
            old_pattern: Pattern to match in filename
            new_pattern: Replacement pattern
            
        Returns:
            Tuple of (success, old_name, new_name_or_error)
        """
        try:
            old_filename = file_path.name
            new_filename = self.generate_new_name(file_path, old_pattern, new_pattern)
            
            # Check if renaming actually occurred
            if new_filename == old_filename:
                return False, old_filename, "找不到匹配的模式"
            
            # Check if target file already exists
            new_path = file_path.parent / new_filename
            if new_path.exists() and new_path != file_path:
                return False, old_filename, f"檔案 {new_filename} 已存在"
            
            # Perform the rename
            file_path.rename(new_path)
            
            self.logger.info(f"Successfully renamed {old_filename} to {new_filename}")
            return True, old_filename, new_filename
            
        except Exception as e:
            error_msg = f"重命名 {file_path.name} 時發生錯誤：{str(e)}"
            self.logger.error(error_msg)
            return False, file_path.name, str(e)
    
    def generate_new_name(self, file_path: Path, old_pattern: str, new_pattern: str) -> str:
        """
        Generate new filename based on patterns (exact logic from desktop version)
        
        Args:
            file_path: Path to the file
            old_pattern: Pattern to match
            new_pattern: Replacement pattern
            
        Returns:
            New filename or original filename if no match
        """
        old_filename = file_path.name
        name_without_ext = file_path.stem
        ext = file_path.suffix
        
        # If no patterns set, return original filename
        if not old_pattern or not new_pattern:
            return old_filename
        
        # Extract number part for {num} placeholder
        num_match = re.search(r'\d+', name_without_ext)
        num = num_match.group() if num_match else ""
        
        # Replace placeholders in new pattern
        new_name = new_pattern
        new_name = new_name.replace("{old}", name_without_ext)
        new_name = new_name.replace("{ext}", ext)
        new_name = new_name.replace("{num}", num)
        
        # If no placeholders used, try regex substitution
        if "{old}" not in new_pattern and "{ext}" not in new_pattern and "{num}" not in new_pattern:
            try:
                # Check if pattern actually matches
                if re.search(old_pattern, old_filename):
                    new_name = re.sub(old_pattern, new_pattern, old_filename)
                else:
                    # No match found, return original filename
                    new_name = old_filename
            except re.error:
                # Invalid regex, return original filename
                new_name = old_filename
        
        return new_name
    
    def validate_rename_pattern(self, old_pattern: str, new_pattern: str) -> Tuple[bool, str]:
        """
        Validate rename patterns
        
        Args:
            old_pattern: Pattern to match
            new_pattern: Replacement pattern
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not old_pattern or not new_pattern:
            return False, "請輸入替換前和替換後的檔案名稱模式"
        
        # Test regex pattern if not using placeholders
        if "{old}" not in new_pattern and "{ext}" not in new_pattern and "{num}" not in new_pattern:
            try:
                re.compile(old_pattern)
            except re.error as e:
                return False, f"無效的正則表達式: {str(e)}"
        
        return True, ""
    
    def preview_rename(self, files: List[Path], old_pattern: str, new_pattern: str) -> List[RenamePreview]:
        """
        Preview rename operations (exact logic from desktop version)
        
        Args:
            files: List of PTS files to preview
            old_pattern: Pattern to match
            new_pattern: Replacement pattern
            
        Returns:
            List of preview results
        """
        previews = []
        
        # If no patterns set, show original filenames
        if not old_pattern or not new_pattern:
            for file_path in files:
                previews.append(RenamePreview(
                    original_name=file_path.name,
                    new_name=file_path.name,
                    will_rename=False,
                    error_message="顯示原始檔案名稱"
                ))
            return previews
        
        for file_path in files:
            old_name = file_path.name
            new_name = self.generate_new_name(file_path, old_pattern, new_pattern)
            
            # Check if renaming will occur
            if new_name == old_name:
                previews.append(RenamePreview(
                    original_name=old_name,
                    new_name=old_name,
                    will_rename=False,
                    error_message="找不到匹配的模式"
                ))
            else:
                # Check for conflicts
                new_path = file_path.parent / new_name
                if new_path.exists() and new_path != file_path:
                    previews.append(RenamePreview(
                        original_name=old_name,
                        new_name=new_name,
                        will_rename=False,
                        error_message=f"檔案 {new_name} 已存在"
                    ))
                else:
                    previews.append(RenamePreview(
                        original_name=old_name,
                        new_name=new_name,
                        will_rename=True
                    ))
        
        return previews
    
    def batch_rename_files(self, files: List[Path], old_pattern: str, new_pattern: str) -> List[PTSRenameResult]:
        """
        Batch rename multiple PTS files
        
        Args:
            files: List of PTS files to rename
            old_pattern: Pattern to match
            new_pattern: Replacement pattern
            
        Returns:
            List of rename results
        """
        results = []
        
        for file_path in files:
            success, old_name, new_name_or_error = self.rename_file(file_path, old_pattern, new_pattern)
            
            result = PTSRenameResult(
                original_name=old_name,
                new_name=new_name_or_error if success else old_name,
                operation="rename",
                success=success,
                error_message=None if success else new_name_or_error
            )
            results.append(result)
        
        return results
    
    def get_pts_files_from_folders(self, folder_paths: List[Path]) -> List[Path]:
        """
        Get all PTS files from specified folders (exact logic from desktop version)
        
        Args:
            folder_paths: List of folder paths to search
            
        Returns:
            List of PTS file paths
        """
        pts_files = []
        
        for folder_path in folder_paths:
            if folder_path.exists() and folder_path.is_dir():
                # Use glob to find all .pts files
                pts_pattern = folder_path / "*.pts"
                pts_files.extend(folder_path.glob("*.pts"))
            else:
                self.logger.warning(f"Folder does not exist or is not a directory: {folder_path}")
        
        return pts_files