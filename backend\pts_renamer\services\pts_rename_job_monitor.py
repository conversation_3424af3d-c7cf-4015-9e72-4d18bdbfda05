"""
PTS Rename Job Status Tracking and Monitoring Service

This module provides comprehensive job status tracking and monitoring capabilities
for PTS file renaming operations. It integrates with the existing monitoring
dashboard and provides real-time status updates.

Features:
- Real-time job status tracking
- Progress monitoring with detailed steps
- Integration with existing monitoring dashboard
- Retry mechanism handling
- Performance metrics collection
- Error tracking and analysis

Requirements: 4.4, 4.5, 4.6, 7.2, 7.3
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

import redis
from loguru import logger

# Import PTS models and services
from ..models.pts_rename_entities import PTSProcessingJob
from ..models.pts_rename_models import (
    PTSRenameJobStatus, 
    PTSProcessingStatus,
    PTSRenameConfig
)
from ..repositories.pts_rename_repository import IPTSRenameRepository

# Import existing monitoring infrastructure
from backend.shared.infrastructure.adapters.database.retry_repository import RetryRepository
from backend.tasks.dramatiq_integration import DramatiqTaskStatus


class PTSJobPhase(Enum):
    """PTS Job processing phases"""
    QUEUED = "queued"
    INITIALIZING = "initializing"
    PROCESSING_FILES = "processing_files"
    GENERATING_QC = "generating_qc"
    CREATING_DIRECTORIES = "creating_directories"
    COMPRESSING = "compressing"
    FINALIZING = "finalizing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class PTSJobMetrics:
    """PTS Job performance metrics"""
    job_id: str
    total_files: int
    processed_files: int
    failed_files: int
    start_time: datetime
    end_time: Optional[datetime] = None
    processing_time: Optional[float] = None
    average_file_time: Optional[float] = None
    throughput_files_per_minute: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None


@dataclass 
class PTSJobStep:
    """Individual processing step tracking"""
    step_id: str
    step_name: str
    phase: PTSJobPhase
    progress: int  # 0-100
    status: PTSProcessingStatus
    message: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    error: Optional[str] = None


class PTSRenameJobMonitor:
    """
    Comprehensive job monitoring service for PTS rename operations
    
    This service provides:
    - Real-time job status tracking
    - Detailed progress monitoring with step-by-step updates
    - Integration with existing monitoring dashboard
    - Performance metrics collection
    - Retry mechanism handling
    - Error analysis and reporting
    """
    
    def __init__(self, 
                 repository: IPTSRenameRepository,
                 redis_client: Optional[redis.Redis] = None):
        """
        Initialize PTS job monitor
        
        Args:
            repository: PTS repository for job data
            redis_client: Redis client for real-time tracking
        """
        self.repository = repository
        self.redis_client = redis_client or self._get_redis_client()
        self.retry_repository = RetryRepository()
        
        # Configuration
        self.status_ttl = 300  # 5 minutes
        self.metrics_ttl = 3600  # 1 hour
        self.step_history_limit = 100
        
    def _get_redis_client(self) -> redis.Redis:
        """Get Redis client with fallback"""
        try:
            return redis.Redis(host="localhost", port=6379, db=0, decode_responses=True)
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}")
            return None
    
    # ============================================================================
    # 📊 Job Status Tracking
    # ============================================================================
    
    async def start_job_tracking(self, 
                                job_id: str, 
                                task_id: str,
                                job_config: Dict[str, Any]) -> bool:
        """
        Start tracking a new PTS job
        
        Args:
            job_id: Job identifier
            task_id: Dramatiq task identifier
            job_config: Job configuration data
            
        Returns:
            Success status
        """
        try:
            logger.info(f"[PTS-MONITOR] Starting job tracking: {job_id}")
            
            # Initialize job metrics
            metrics = PTSJobMetrics(
                job_id=job_id,
                total_files=job_config.get('total_files', 0),
                processed_files=0,
                failed_files=0,
                start_time=datetime.now()
            )
            
            # Store in Redis for real-time access
            if self.redis_client:
                await self._store_job_metrics(metrics)
                await self._store_job_status(job_id, task_id, PTSJobPhase.QUEUED, 0, "Job queued for processing")
            
            # Initialize step tracking
            initial_step = PTSJobStep(
                step_id=f"{job_id}_init",
                step_name="Job Initialization",
                phase=PTSJobPhase.INITIALIZING,
                progress=0,
                status=PTSProcessingStatus.PENDING,
                message="Job queued and ready for processing",
                start_time=datetime.now()
            )
            
            await self._add_job_step(job_id, initial_step)
            
            logger.info(f"[PTS-MONITOR] Job tracking started: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to start job tracking: {e}")
            return False
    
    async def update_job_progress(self,
                                 job_id: str,
                                 task_id: str,
                                 phase: PTSJobPhase,
                                 progress: int,
                                 message: str,
                                 step_details: Optional[Dict[str, Any]] = None) -> bool:
        """
        Update job progress with detailed tracking
        
        Args:
            job_id: Job identifier
            task_id: Task identifier
            phase: Current processing phase
            progress: Progress percentage (0-100)
            message: Status message
            step_details: Additional step details
            
        Returns:
            Success status
        """
        try:
            # Update main job status
            await self._store_job_status(job_id, task_id, phase, progress, message)
            
            # Add detailed step if provided
            if step_details:
                step = PTSJobStep(
                    step_id=step_details.get('step_id', f"{job_id}_{phase.value}_{datetime.now().timestamp()}"),
                    step_name=step_details.get('step_name', phase.value.replace('_', ' ').title()),
                    phase=phase,
                    progress=progress,
                    status=PTSProcessingStatus.PROCESSING,
                    message=message,
                    start_time=datetime.now()
                )
                await self._add_job_step(job_id, step)
            
            # Update metrics if processing files
            if phase == PTSJobPhase.PROCESSING_FILES and step_details:
                await self._update_processing_metrics(job_id, step_details)
            
            logger.debug(f"[PTS-MONITOR] Progress updated: {job_id} -> {progress}% ({phase.value})")
            return True
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to update job progress: {e}")
            return False
    
    async def complete_job_tracking(self,
                                   job_id: str,
                                   task_id: str,
                                   result: Dict[str, Any]) -> bool:
        """
        Complete job tracking with final results
        
        Args:
            job_id: Job identifier
            task_id: Task identifier
            result: Final job results
            
        Returns:
            Success status
        """
        try:
            logger.info(f"[PTS-MONITOR] Completing job tracking: {job_id}")
            
            # Update final status
            await self._store_job_status(
                job_id, task_id, PTSJobPhase.COMPLETED, 100,
                f"Job completed successfully. Processed {result.get('processed_files', 0)} files."
            )
            
            # Finalize metrics
            await self._finalize_job_metrics(job_id, result)
            
            # Add completion step
            completion_step = PTSJobStep(
                step_id=f"{job_id}_complete",
                step_name="Job Completion",
                phase=PTSJobPhase.COMPLETED,
                progress=100,
                status=PTSProcessingStatus.COMPLETED,
                message="All processing completed successfully",
                start_time=datetime.now(),
                end_time=datetime.now()
            )
            
            await self._add_job_step(job_id, completion_step)
            
            # Store final result for download
            if self.redis_client:
                result_key = f"pts_job_result:{job_id}"
                await self.redis_client.setex(
                    result_key, self.metrics_ttl, 
                    json.dumps(result, default=str)
                )
            
            logger.info(f"[PTS-MONITOR] Job tracking completed: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to complete job tracking: {e}")
            return False
    
    async def fail_job_tracking(self,
                               job_id: str,
                               task_id: str,
                               error: str,
                               error_type: str,
                               retryable: bool = True) -> bool:
        """
        Handle job failure tracking
        
        Args:
            job_id: Job identifier
            task_id: Task identifier 
            error: Error message
            error_type: Type of error
            retryable: Whether job can be retried
            
        Returns:
            Success status
        """
        try:
            logger.error(f"[PTS-MONITOR] Job failed: {job_id} - {error}")
            
            # Determine phase based on retryable status
            phase = PTSJobPhase.RETRYING if retryable else PTSJobPhase.FAILED
            
            # Update job status
            await self._store_job_status(
                job_id, task_id, phase, 0,
                f"Job failed: {error}"
            )
            
            # Add failure step
            failure_step = PTSJobStep(
                step_id=f"{job_id}_failure",
                step_name="Job Failure",
                phase=phase,
                progress=0,
                status=PTSProcessingStatus.FAILED,
                message=f"Job failed: {error}",
                start_time=datetime.now(),
                end_time=datetime.now(),
                error=error
            )
            
            await self._add_job_step(job_id, failure_step)
            
            # Record retry if retryable
            if retryable:
                await self._record_retry_attempt(job_id, task_id, error, error_type)
            
            # Update failure metrics
            await self._update_failure_metrics(job_id, error, error_type)
            
            return True
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to handle job failure: {e}")
            return False
    
    # ============================================================================
    # 📈 Status Retrieval and Monitoring
    # ============================================================================
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive job status
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job status with details or None if not found
        """
        try:
            # Get from Redis first (real-time)
            if self.redis_client:
                status_key = f"pts_job_status:{job_id}"
                redis_status = await self.redis_client.get(status_key)
                
                if redis_status:
                    status_data = json.loads(redis_status)
                    
                    # Enhance with additional data
                    metrics = await self._get_job_metrics(job_id)
                    steps = await self._get_job_steps(job_id)
                    
                    return {
                        **status_data,
                        'metrics': metrics,
                        'steps': steps,
                        'source': 'redis'
                    }
            
            # Fallback to repository
            job = await self.repository.get_job(job_id)
            if job:
                return {
                    'job_id': job.job_id,
                    'status': job.status.value if hasattr(job.status, 'value') else str(job.status),
                    'progress': getattr(job, 'progress_percentage', 0),
                    'message': getattr(job, 'status_message', 'No message'),
                    'created_at': job.created_at.isoformat() if job.created_at else None,
                    'updated_at': job.updated_at.isoformat() if job.updated_at else None,
                    'source': 'repository'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get job status: {e}")
            return None
    
    async def get_job_metrics(self, job_id: str) -> Optional[PTSJobMetrics]:
        """
        Get job performance metrics
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job metrics or None if not found
        """
        try:
            return await self._get_job_metrics(job_id)
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get job metrics: {e}")
            return None
    
    async def get_active_jobs(self) -> List[Dict[str, Any]]:
        """
        Get list of currently active jobs
        
        Returns:
            List of active job statuses
        """
        try:
            active_jobs = []
            
            if self.redis_client:
                # Scan for active job keys
                pattern = "pts_job_status:*"
                for key in self.redis_client.scan_iter(match=pattern):
                    job_id = key.split(':')[1]
                    status = await self.get_job_status(job_id)
                    if status and status.get('status') not in ['completed', 'failed']:
                        active_jobs.append(status)
            
            return active_jobs
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get active jobs: {e}")
            return []
    
    async def get_job_history(self, 
                             limit: int = 50,
                             status_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get job history from repository
        
        Args:
            limit: Maximum number of jobs to return
            status_filter: Optional status filter
            
        Returns:
            List of historical jobs
        """
        try:
            # This would need to be implemented in the repository
            jobs = await self.repository.get_job_history(limit, status_filter)
            return [self._job_to_dict(job) for job in jobs]
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get job history: {e}")
            return []
    
    # ============================================================================
    # 🔧 Retry and Error Handling
    # ============================================================================
    
    async def handle_job_retry(self, 
                              job_id: str, 
                              original_task_id: str,
                              new_task_id: str,
                              retry_attempt: int) -> bool:
        """
        Handle job retry tracking
        
        Args:
            job_id: Job identifier
            original_task_id: Original task ID that failed
            new_task_id: New task ID for retry
            retry_attempt: Retry attempt number
            
        Returns:
            Success status
        """
        try:
            logger.info(f"[PTS-MONITOR] Handling job retry: {job_id} (attempt {retry_attempt})")
            
            # Update job status to retrying
            await self._store_job_status(
                job_id, new_task_id, PTSJobPhase.RETRYING, 0,
                f"Retrying job (attempt {retry_attempt})"
            )
            
            # Add retry step
            retry_step = PTSJobStep(
                step_id=f"{job_id}_retry_{retry_attempt}",
                step_name=f"Retry Attempt {retry_attempt}",
                phase=PTSJobPhase.RETRYING,
                progress=0,
                status=PTSProcessingStatus.PROCESSING,
                message=f"Starting retry attempt {retry_attempt}",
                start_time=datetime.now()
            )
            
            await self._add_job_step(job_id, retry_step)
            
            # Record retry in retry repository
            await self.retry_repository.record_retry(
                entity_id=job_id,
                operation='pts_rename_job',
                attempt_number=retry_attempt,
                original_error="Previous attempt failed",
                retry_strategy='exponential_backoff'
            )
            
            return True
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to handle job retry: {e}")
            return False
    
    # ============================================================================
    # 🔒 Private Helper Methods
    # ============================================================================
    
    async def _store_job_status(self,
                               job_id: str,
                               task_id: str,
                               phase: PTSJobPhase,
                               progress: int,
                               message: str):
        """Store job status in Redis"""
        if not self.redis_client:
            return
        
        try:
            status_data = {
                'job_id': job_id,
                'task_id': task_id,
                'phase': phase.value,
                'status': phase.value,  # Compatibility
                'progress': progress,
                'message': message,
                'updated_at': datetime.now().isoformat()
            }
            
            status_key = f"pts_job_status:{job_id}"
            await self.redis_client.setex(
                status_key, self.status_ttl,
                json.dumps(status_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to store job status: {e}")
    
    async def _store_job_metrics(self, metrics: PTSJobMetrics):
        """Store job metrics in Redis"""
        if not self.redis_client:
            return
        
        try:
            metrics_data = {
                'job_id': metrics.job_id,
                'total_files': metrics.total_files,
                'processed_files': metrics.processed_files,
                'failed_files': metrics.failed_files,
                'start_time': metrics.start_time.isoformat(),
                'end_time': metrics.end_time.isoformat() if metrics.end_time else None,
                'processing_time': metrics.processing_time,
                'average_file_time': metrics.average_file_time,
                'throughput_files_per_minute': metrics.throughput_files_per_minute,
                'memory_usage_mb': metrics.memory_usage_mb,
                'cpu_usage_percent': metrics.cpu_usage_percent
            }
            
            metrics_key = f"pts_job_metrics:{metrics.job_id}"
            await self.redis_client.setex(
                metrics_key, self.metrics_ttl,
                json.dumps(metrics_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to store job metrics: {e}")
    
    async def _get_job_metrics(self, job_id: str) -> Optional[PTSJobMetrics]:
        """Get job metrics from Redis"""
        if not self.redis_client:
            return None
        
        try:
            metrics_key = f"pts_job_metrics:{job_id}"
            metrics_data = await self.redis_client.get(metrics_key)
            
            if metrics_data:
                data = json.loads(metrics_data)
                return PTSJobMetrics(
                    job_id=data['job_id'],
                    total_files=data['total_files'],
                    processed_files=data['processed_files'],
                    failed_files=data['failed_files'],
                    start_time=datetime.fromisoformat(data['start_time']),
                    end_time=datetime.fromisoformat(data['end_time']) if data['end_time'] else None,
                    processing_time=data.get('processing_time'),
                    average_file_time=data.get('average_file_time'),
                    throughput_files_per_minute=data.get('throughput_files_per_minute'),
                    memory_usage_mb=data.get('memory_usage_mb'),
                    cpu_usage_percent=data.get('cpu_usage_percent')
                )
            
            return None
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get job metrics: {e}")
            return None
    
    async def _add_job_step(self, job_id: str, step: PTSJobStep):
        """Add job step to tracking"""
        if not self.redis_client:
            return
        
        try:
            step_data = {
                'step_id': step.step_id,
                'step_name': step.step_name,
                'phase': step.phase.value,
                'progress': step.progress,
                'status': step.status.value if hasattr(step.status, 'value') else str(step.status),
                'message': step.message,
                'start_time': step.start_time.isoformat(),
                'end_time': step.end_time.isoformat() if step.end_time else None,
                'duration': step.duration,
                'error': step.error
            }
            
            steps_key = f"pts_job_steps:{job_id}"
            await self.redis_client.lpush(steps_key, json.dumps(step_data, default=str))
            await self.redis_client.ltrim(steps_key, 0, self.step_history_limit - 1)
            await self.redis_client.expire(steps_key, self.metrics_ttl)
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to add job step: {e}")
    
    async def _get_job_steps(self, job_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get job steps from Redis"""
        if not self.redis_client:
            return []
        
        try:
            steps_key = f"pts_job_steps:{job_id}"
            steps_data = await self.redis_client.lrange(steps_key, 0, limit - 1)
            
            return [json.loads(step_data) for step_data in steps_data]
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get job steps: {e}")
            return []
    
    async def _update_processing_metrics(self, job_id: str, step_details: Dict[str, Any]):
        """Update processing metrics"""
        try:
            metrics = await self._get_job_metrics(job_id)
            if metrics:
                metrics.processed_files = step_details.get('processed_files', metrics.processed_files)
                metrics.failed_files = step_details.get('failed_files', metrics.failed_files)
                
                # Calculate throughput
                if metrics.start_time:
                    elapsed = (datetime.now() - metrics.start_time).total_seconds()
                    if elapsed > 0:
                        metrics.throughput_files_per_minute = (metrics.processed_files / elapsed) * 60
                
                await self._store_job_metrics(metrics)
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to update processing metrics: {e}")
    
    async def _finalize_job_metrics(self, job_id: str, result: Dict[str, Any]):
        """Finalize job metrics with final results"""
        try:
            metrics = await self._get_job_metrics(job_id)
            if metrics:
                metrics.end_time = datetime.now()
                metrics.processing_time = result.get('processing_time')
                metrics.processed_files = result.get('processed_files', 0)
                metrics.failed_files = result.get('failed_files', 0)
                
                if metrics.processing_time and metrics.processed_files:
                    metrics.average_file_time = metrics.processing_time / metrics.processed_files
                    metrics.throughput_files_per_minute = (metrics.processed_files / metrics.processing_time) * 60
                
                await self._store_job_metrics(metrics)
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to finalize job metrics: {e}")
    
    async def _update_failure_metrics(self, job_id: str, error: str, error_type: str):
        """Update failure metrics"""
        try:
            # Store failure information
            if self.redis_client:
                failure_data = {
                    'job_id': job_id,
                    'error': error,
                    'error_type': error_type,
                    'failed_at': datetime.now().isoformat()
                }
                
                failure_key = f"pts_job_failure:{job_id}"
                await self.redis_client.setex(
                    failure_key, self.metrics_ttl,
                    json.dumps(failure_data, default=str)
                )
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to update failure metrics: {e}")
    
    async def _record_retry_attempt(self, job_id: str, task_id: str, error: str, error_type: str):
        """Record retry attempt"""
        try:
            await self.retry_repository.record_retry(
                entity_id=job_id,
                operation='pts_rename_job',
                attempt_number=1,  # Would need to track this properly
                original_error=error,
                retry_strategy='exponential_backoff',
                metadata={
                    'task_id': task_id,
                    'error_type': error_type,
                    'retry_timestamp': datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to record retry attempt: {e}")
    
    def _job_to_dict(self, job: PTSProcessingJob) -> Dict[str, Any]:
        """Convert job entity to dictionary"""
        return {
            'job_id': job.job_id,
            'upload_id': job.upload_id,
            'status': job.status.value if hasattr(job.status, 'value') else str(job.status),
            'total_files': job.total_files,
            'files_processed': getattr(job, 'files_processed', 0),
            'progress': getattr(job, 'progress_percentage', 0),
            'created_at': job.created_at.isoformat() if job.created_at else None,
            'updated_at': job.updated_at.isoformat() if job.updated_at else None,
            'download_url': getattr(job, 'download_url', None)
        }


# ============================================================================
# 📋 Monitoring Integration Functions
# ============================================================================

async def get_pts_monitoring_data() -> Dict[str, Any]:
    """
    Get PTS monitoring data for dashboard integration
    
    Returns:
        Monitoring data for dashboard display
    """
    try:
        # This would be called by the monitoring dashboard
        monitor = PTSRenameJobMonitor(None)  # Repository would be injected
        
        active_jobs = await monitor.get_active_jobs()
        
        # Calculate summary statistics
        total_active = len(active_jobs)
        processing_jobs = len([j for j in active_jobs if j.get('status') == 'processing_files'])
        queued_jobs = len([j for j in active_jobs if j.get('status') == 'queued'])
        
        return {
            'service_name': 'PTS File Renamer',
            'status': 'active',
            'total_active_jobs': total_active,
            'processing_jobs': processing_jobs,
            'queued_jobs': queued_jobs,
            'active_jobs': active_jobs[:10],  # Limit for dashboard
            'last_updated': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[PTS-MONITOR] Failed to get monitoring data: {e}")
        return {
            'service_name': 'PTS File Renamer',
            'status': 'error',
            'error': str(e),
            'last_updated': datetime.now().isoformat()
        }


# Export monitoring classes and functions
__all__ = [
    'PTSRenameJobMonitor',
    'PTSJobPhase',
    'PTSJobMetrics',
    'PTSJobStep',
    'get_pts_monitoring_data'
]