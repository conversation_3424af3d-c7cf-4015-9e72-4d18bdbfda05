#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON> Hooks 啟動腳本
用於測試和運行 <PERSON> Hooks 系統
"""

import asyncio
import sys
import os
import argparse
from pathlib import Path

# 設定標準輸出編碼
if sys.platform.startswith('win'):
    import locale
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except:
        pass

# 添加當前目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

try:
    from hooks.main import ClaudeHooksManager
    from hooks.config_manager import ConfigManager
except ImportError:
    # 使用簡化版本
    from hooks.main_simple import ClaudeHooksManager
    
    class ConfigManager:
        def __init__(self, repo_root):
            self.repo_root = repo_root
            self.config = {'file_size_limit': 500}
        
        def create_user_config_template(self):
            safe_print("✅ 使用預設配置")
        
        def validate_config(self):
            return []

async def main():
    """主函數 - 已禁用"""
    print("⚠️  <PERSON> Hooks 已禁用")
    print("📝 如需重新啟用，請使用: make enable-hooks")
    return
    # 檢查是否從 Claude Code Hook 觸發
    claude_files = os.environ.get('CLAUDE_FILE_PATHS', '')
    claude_tool = os.environ.get('CLAUDE_TOOL_NAME', '')
    is_claude_hook = bool(claude_files or claude_tool)
    
    try:
        print(f"🤖 觸發來源: {'Claude Code Hook' if is_claude_hook else '手動執行'}")
        if claude_files:
            print(f"📁 變更的檔案: {claude_files}")
        if claude_tool:
            print(f"🔧 使用的工具: {claude_tool}")
    except UnicodeEncodeError:
        print(f"[HOOK] 觸發來源: {'Claude Code Hook' if is_claude_hook else '手動執行'}")
        if claude_files:
            print(f"[FILES] 變更的檔案: {claude_files}")
        if claude_tool:
            print(f"[TOOL] 使用的工具: {claude_tool}")
    
    parser = argparse.ArgumentParser(description='Claude Hooks 系統')
    parser.add_argument('--config', '-c', help='配置檔案路徑')
    parser.add_argument('--analyze', '-a', action='store_true', help='分析專案')
    parser.add_argument('--files', '-f', nargs='*', help='指定要分析的檔案')
    parser.add_argument('--split', '-s', action='store_true', help='自動拆分超大檔案')
    parser.add_argument('--clean', action='store_true', help='清理重複代碼')
    parser.add_argument('--optimize', '-o', action='store_true', help='優化導入')
    parser.add_argument('--validate', '-v', action='store_true', help='驗證變更')
    parser.add_argument('--init', action='store_true', help='初始化配置')
    parser.add_argument('--dry-run', action='store_true', help='乾跑模式（僅顯示建議）')
    
    args = parser.parse_args()
    
    # 記錄到開發日誌
    log_to_dev_log(f"[Hook觸發] run_hooks.py --analyze - 工具: {claude_tool}")
    
    # 如果從 Claude Code 觸發，自動設定分析模式和檔案
    if is_claude_hook:
        args.analyze = True  # 自動啟用分析
        if claude_files and not args.files:
            # 解析檔案路徑，處理可能的空格
            file_list = [f.strip() for f in claude_files.split() if f.strip()]
            # 只分析 Python 檔案
            python_files = [f for f in file_list if f.endswith(('.py', '.pyi'))]
            if python_files:
                args.files = python_files
                print(f"📊 將分析 Python 檔案: {python_files}")
            else:
                print(f"ℹ️ 沒有 Python 檔案需要分析")
                return
    
    # 初始化 - 確保在正確的專案目錄
    script_dir = Path(__file__).parent.parent  # .claude 的父目錄
    repo_root = script_dir
    
    # 確保工作目錄正確
    os.chdir(repo_root)
    while not (repo_root / '.git').exists() and repo_root.parent != repo_root:
        repo_root = repo_root.parent
    
    if not (repo_root / '.git').exists():
        repo_root = Path.cwd()
    
    print(f"🏠 專案根目錄: {repo_root}")
    
    # 創建 .claude 目錄
    claude_dir = repo_root / '.claude'
    claude_dir.mkdir(exist_ok=True)
    
    # 初始化配置
    if args.init:
        config_manager = ConfigManager(repo_root)
        config_manager.create_user_config_template()
        safe_print("✅ 配置初始化完成")
        return
    
    # 載入配置
    config_manager = ConfigManager(repo_root)
    
    # 驗證配置
    config_errors = config_manager.validate_config()
    if config_errors:
        safe_print("⚠️  配置驗證錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return
    
    # 創建 Hooks 管理器
    try:
        hooks_manager = ClaudeHooksManager(repo_root, config_manager.config)
    except Exception as e:
        # 使用簡化版本
        hooks_manager = ClaudeHooksManager(repo_root, config_manager.config if hasattr(config_manager, 'config') else None)
    
    try:
        if args.analyze:
            print("🔍 開始專案分析...")
            analysis_result = await hooks_manager.analyze_project(args.files)
            
            # 顯示分析結果
            print_analysis_results(analysis_result)
            
            # 根據分析結果執行操作
            if not args.dry_run:
                await execute_suggested_actions(hooks_manager, analysis_result, args)
        
        elif args.split:
            print("✂️  開始檔案拆分...")
            # 先分析找出需要拆分的檔案
            analysis_result = await hooks_manager.analyze_project(args.files)
            file_size_issues = analysis_result.get('file_size', {}).get('oversized_files', [])
            
            if file_size_issues:
                split_results = await hooks_manager.split_oversized_files(file_size_issues)
                print_split_results(split_results)
            else:
                safe_print("✅ 沒有檔案需要拆分")
        
        elif args.clean:
            print("🧹 開始清理重複代碼...")
            # 先分析找出重複代碼
            analysis_result = await hooks_manager.analyze_project(args.files)
            duplicate_issues = analysis_result.get('duplicates', {})
            
            if duplicate_issues.get('duplicates'):
                clean_results = await hooks_manager.clean_duplicates(duplicate_issues)
                print_clean_results(clean_results)
            else:
                safe_print("✅ 沒有發現重複代碼")
        
        elif args.optimize:
            print("🔧 開始優化導入...")
            # 先分析 import 問題
            analysis_result = await hooks_manager.analyze_project(args.files)
            import_issues = analysis_result.get('imports', {})
            
            if import_issues.get('optimization_opportunities'):
                optimize_results = await hooks_manager.optimize_imports(import_issues)
                print_optimize_results(optimize_results)
            else:
                safe_print("✅ 導入已經是最優狀態")
        
        elif args.validate:
            print("🔍 開始驗證專案...")
            # 模擬變更來觸發驗證
            mock_changes = [{'operation': 'validation_test'}]
            validation_result = await hooks_manager.validate_changes(mock_changes, 'manual_validation')
            print_validation_results(validation_result)
        
        else:
            # 預設：完整分析
            print("🚀 執行完整分析...")
            analysis_result = await hooks_manager.analyze_project(args.files)
            print_analysis_results(analysis_result)
            
            if not args.dry_run:
                print("\n💡 建議的操作:")
                print("   --split    : 拆分超大檔案")
                print("   --clean    : 清理重複代碼")
                print("   --optimize : 優化導入語句")
                print("   --validate : 驗證專案狀態")
    
    except Exception as e:
        print(f"❌ 執行失敗: {e}")
        import traceback
        traceback.print_exc()

def print_analysis_results(results: dict):
    """顯示分析結果"""
    print("\n📊 分析結果:")
    
    # 檔案大小分析
    if 'file_size' in results:
        file_size = results['file_size']
        oversized = file_size.get('oversized_files', [])
        if oversized:
            print(f"   📏 發現 {len(oversized)} 個超大檔案:")
            for file_info in oversized[:5]:  # 只顯示前5個
                print(f"      - {file_info['file_path']} ({file_info['current_lines']} 行)")
        else:
            safe_print("   ✅ 檔案大小正常")
    
    # 重複代碼檢測
    if 'duplicates' in results:
        duplicates = results['duplicates']
        duplicate_count = len(duplicates.get('duplicates', []))
        if duplicate_count > 0:
            print(f"   🔄 發現 {duplicate_count} 組重複代碼")
        else:
            safe_print("   ✅ 沒有發現重複代碼")
    
    # 導入分析
    if 'imports' in results:
        imports = results['imports']
        opportunities = imports.get('optimization_opportunities', [])
        if opportunities:
            print(f"   🔧 發現 {len(opportunities)} 個導入優化機會")
        else:
            safe_print("   ✅ 導入狀態良好")
    
    # 品質檢查
    if 'quality' in results:
        quality = results['quality']
        issues = quality.get('quality_issues', [])
        score = quality.get('quality_score', 0)
        print(f"   ⭐ 代碼品質評分: {score:.1f}/100")
        if issues:
            safe_print(f"   ⚠️  發現 {len(issues)} 個品質問題")

def print_split_results(results: dict):
    """顯示拆分結果"""
    print("\n✂️  拆分結果:")
    for result in results:
        if result['status'] == 'success':
            safe_print(f"   ✅ {result['original_file']}")
            print(f"      原始: {result['original_lines']} 行")
            print(f"      拆分為: {len(result['new_files'])} 個檔案")
        else:
            print(f"   ❌ {result.get('original_file', 'unknown')}: {result.get('message', 'unknown error')}")

def print_clean_results(results: dict):
    """顯示清理結果"""
    print("\n🧹 清理結果:")
    if results['status'] == 'success':
        cleaned = results['cleaned_items']
        safe_print(f"   ✅ 清理了 {len(cleaned)} 個重複項目")
        print(f"   💾 節省了 {results['space_saved_lines']} 行代碼")
    else:
        print(f"   ❌ 清理失敗: {results.get('message', 'unknown error')}")

def print_optimize_results(results: dict):
    """顯示優化結果"""
    print("\n🔧 優化結果:")
    if results['status'] == 'success':
        optimizations = results['optimizations']
        print(f"   🔍 發現 {len(optimizations)} 個優化機會")
        
        if results.get('applied_changes'):
            applied = results['applied_changes']
            safe_print(f"   ✅ 自動應用了 {len(applied)} 個安全變更")
    else:
        print(f"   ❌ 優化失敗: {results.get('message', 'unknown error')}")

def print_validation_results(results: dict):
    """顯示驗證結果"""
    print("\n🔍 驗證結果:")
    status = results['status']
    
    if status == 'passed':
        safe_print("   ✅ 所有驗證通過")
    elif status == 'passed_with_warnings':
        safe_print("   ⚠️  驗證通過但有警告")
        warnings = results.get('warnings', [])
        print(f"      警告數量: {len(warnings)}")
    else:
        print(f"   ❌ 驗證失敗: {status}")
        errors = results.get('errors', [])
        print(f"      錯誤數量: {len(errors)}")

async def execute_suggested_actions(hooks_manager, analysis_result: dict, args):
    """執行建議的操作"""
    print("\n🎯 執行建議的操作...")
    
    # 檔案拆分
    oversized_files = analysis_result.get('file_size', {}).get('oversized_files', [])
    if oversized_files:
        print(f"   ✂️  拆分 {len(oversized_files)} 個超大檔案...")
        split_results = await hooks_manager.split_oversized_files(oversized_files)
        
        # 驗證拆分結果
        changes = [{'type': 'file_split', 'results': split_results}]
        validation_result = await hooks_manager.validate_changes(changes, 'auto_split')
        
        if validation_result['status'] not in ['passed', 'passed_with_warnings']:
            safe_print("   ⚠️  拆分後驗證失敗，建議檢查結果")
    
    # 重複代碼清理
    duplicates = analysis_result.get('duplicates', {}).get('duplicates', [])
    if duplicates:
        print(f"   🧹 清理 {len(duplicates)} 組重複代碼...")
        clean_results = await hooks_manager.clean_duplicates(analysis_result['duplicates'])
        
        # 驗證清理結果
        changes = [{'type': 'duplicate_clean', 'results': clean_results}]
        validation_result = await hooks_manager.validate_changes(changes, 'auto_clean')
        
        if validation_result['status'] not in ['passed', 'passed_with_warnings']:
            safe_print("   ⚠️  清理後驗證失敗，建議檢查結果")

def log_to_dev_log(message):
    """記錄到開發日誌"""
    try:
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_file = Path(__file__).parent / 'dev_log.txt'
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")
    except Exception:
        pass  # 靜默失敗，不影響主要功能

def safe_print(text):
    """安全的print函數，處理Unicode編碼問題"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 替換emoji為文字符號
        emoji_replacements = {
            '🤖': '[ROBOT]',
            '📁': '[FILE]', 
            '🔧': '[TOOL]',
            '✅': '[OK]',
            '⚠️': '[WARN]',
            '🎉': '[SUCCESS]',
            '📝': '[DOC]',
            '🔍': '[SEARCH]',
            '🧪': '[TEST]',
            '🚀': '[ROCKET]',
            '💥': '[ERROR]',
            '👋': '[WAVE]'
        }
        clean_text = text
        for emoji, replacement in emoji_replacements.items():
            clean_text = clean_text.replace(emoji, replacement)
        print(clean_text)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        safe_print("\n👋 操作已取消")
    except Exception as e:
        safe_print(f"\n💥 程式異常退出: {e}")
        sys.exit(1)