# 資料庫整合決策 - 回到統一資料庫

## 用戶決策
用戶希望將 PTS Renamer 的專用資料庫 `pts_rename.db` 整合回根目錄的 `outlook.db`，使用統一的資料庫管理。

## 整合優勢
- **簡化監控**: 只需要監控一個資料庫檔案
- **統一管理**: 所有系統資料在同一個資料庫中
- **減少複雜性**: 避免多資料庫配置和維護
- **一致性**: 與現有系統架構保持一致

## 需要執行的變更
1. **程式碼回滾**: 將所有 `data/pts_rename.db` 改回 `outlook.db`
2. **路徑修正**: 更新所有預設路徑配置
3. **文檔更新**: 修正規格文檔以反映統一資料庫架構
4. **資料庫初始化**: 確保 PTS 表格在 outlook.db 中正確建立

## 影響的檔案
- `pts_rename_database_connection.py`
- `pts_rename_database.py` 
- `pts_rename_sql_repository.py`
- `pts_rename_repository.py`
- `pts_rename_service_factory.py`
- `repositories/__init__.py`
- 規格文檔 (design.md, pts-renamer.md)

## 技術考量
- 需要確保 PTS 表格不與現有表格衝突
- 維持 PTS 功能的獨立性但使用共享資料庫
- 保持現有監控系統不變