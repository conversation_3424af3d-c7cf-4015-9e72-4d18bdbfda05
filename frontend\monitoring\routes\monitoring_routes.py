"""
監控路由 - 核心資料庫管理功能
"""

from flask import Blueprint, render_template, jsonify, request, make_response
from datetime import datetime
import logging
import os
import sqlite3

# 設定日誌
logger = logging.getLogger(__name__)

# 創建監控藍圖
monitoring_bp = Blueprint('monitoring', __name__,
                         url_prefix='/monitoring',
                         template_folder='../templates',
                         static_folder='../static')

# 資料庫路徑 - 修正為正確的專案根目錄路徑
import sys
from pathlib import Path

# 獲取專案根目錄
project_root = Path(__file__).parent.parent.parent.parent
DATABASE_PATH = project_root / 'email_inbox.db'
DATABASE_PATH = str(DATABASE_PATH)

print(f"Database path: {DATABASE_PATH}")  # 調試用
print(f"Database exists: {os.path.exists(DATABASE_PATH)}")  # 調試用

@monitoring_bp.route('/database-manager')
def database_manager():
    """資料庫管理主頁面"""
    return render_template('database_manager.html')

@monitoring_bp.route('/api/database/info')
def get_database_info():
    """獲取資料庫基本資訊"""
    try:
        # 獲取資料庫檔案大小
        if os.path.exists(DATABASE_PATH):
            size_bytes = os.path.getsize(DATABASE_PATH)
            size_mb = round(size_bytes / (1024 * 1024), 2)
            size_str = f"{size_mb} MB"
        else:
            size_str = "檔案不存在"
        
        # 連接資料庫獲取表格資訊
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 獲取所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        table_info = []
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            table_info.append({
                'name': table_name,
                'count': count
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'database_path': DATABASE_PATH,
                'database_size': size_str,
                'tables': table_info
            }
        })
        
    except Exception as e:
        logger.error(f"獲取資料庫資訊失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取資料庫資訊失敗: {str(e)}'
        }), 500

@monitoring_bp.route('/api/database/table/<table_name>')
def get_table_data(table_name):
    """獲取指定表格的資料"""
    try:
        search_term = request.args.get('search', '')
        format_type = request.args.get('format', 'json')
        
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row  # 讓結果可以像字典一樣存取
        cursor = conn.cursor()
        
        # 獲取表格結構
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns_info = cursor.fetchall()
        columns = [col[1] for col in columns_info]
        
        # 構建查詢
        if search_term:
            # 在所有文字欄位中搜尋
            text_columns = []
            for col in columns:
                if col not in ['id']:  # 排除數字欄位
                    text_columns.append(f"{col} LIKE ?")
            
            if text_columns:
                where_clause = " OR ".join(text_columns)
                query = f"SELECT * FROM {table_name} WHERE {where_clause} LIMIT 1000"
                params = [f'%{search_term}%'] * len(text_columns)
            else:
                query = f"SELECT * FROM {table_name} LIMIT 1000"
                params = []
        else:
            query = f"SELECT * FROM {table_name} LIMIT 1000"
            params = []
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 轉換為字典列表
        data_rows = []
        for row in rows:
            row_dict = {}
            for i, col in enumerate(columns):
                row_dict[col] = row[i]
            data_rows.append(row_dict)
        
        conn.close()
        
        if format_type == 'csv':
            # 返回 CSV 格式
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=columns)
            writer.writeheader()
            writer.writerows(data_rows)
            
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv'
            response.headers['Content-Disposition'] = f'attachment; filename={table_name}.csv'
            return response
        
        return jsonify({
            'success': True,
            'data': {
                'columns': columns,
                'rows': data_rows,
                'total_rows': len(data_rows)
            }
        })
        
    except Exception as e:
        logger.error(f"獲取表格資料失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取表格資料失敗: {str(e)}'
        }), 500

@monitoring_bp.route('/api/database/stats')
def get_database_stats():
    """獲取資料庫統計資訊"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        stats = {}
        
        # 獲取各表格的統計資訊
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            stats[table_name] = count
        
        # 如果有 emails 表格，獲取額外統計
        if 'emails' in [t[0] for t in tables]:
            # 下載成功統計
            cursor.execute("SELECT COUNT(*) FROM emails WHERE download_success = 1")
            download_success_count = cursor.fetchone()[0]
            
            # 處理成功統計
            cursor.execute("SELECT COUNT(*) FROM emails WHERE processing_success = 1")
            processing_success_count = cursor.fetchone()[0]
            
            stats['download_success_count'] = download_success_count
            stats['processing_success_count'] = processing_success_count
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"獲取資料庫統計失敗: {e}")
        return jsonify({
            'success': False,
            'message': f'獲取資料庫統計失敗: {str(e)}'
        }), 500

@monitoring_bp.route('/api/database/table/<table_name>/batch-delete', methods=['POST'])
def batch_delete_table_records(table_name):
    """批量刪除表格記錄"""
    try:
        # 驗證表格名稱，防止 SQL 注入
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # 檢查表格是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            return jsonify({
                'success': False,
                'message': f'表格 {table_name} 不存在'
            }), 404
        
        # 獲取請求資料
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'message': '缺少 ids 參數'
            }), 400
        
        ids = data['ids']
        if not isinstance(ids, list) or not ids:
            return jsonify({
                'success': False,
                'message': 'ids 必須是非空數組'
            }), 400
        
        # 構建安全的 DELETE 語句
        placeholders = ','.join(['?'] * len(ids))
        delete_sql = f"DELETE FROM {table_name} WHERE id IN ({placeholders})"
        
        logger.info(f"執行批量刪除 - 表格: {table_name}, 記錄數: {len(ids)}")
        logger.debug(f"SQL: {delete_sql}, IDs: {ids}")
        
        # 執行刪除操作
        cursor.execute(delete_sql, ids)
        affected_rows = cursor.rowcount
        
        # 提交更改
        conn.commit()
        
        logger.info(f"批量刪除成功 - 表格: {table_name}, 刪除記錄數: {affected_rows}")
        
        return jsonify({
            'success': True,
            'message': f'成功刪除 {affected_rows} 筆記錄',
            'affected_rows': affected_rows,
            'table_name': table_name
        })
        
    except sqlite3.Error as e:
        logger.error(f"批量刪除 SQL 錯誤 - 表格: {table_name}, 錯誤: {e}")
        return jsonify({
            'success': False,
            'message': f'資料庫操作失敗: {str(e)}'
        }), 500
        
    except Exception as e:
        logger.error(f"批量刪除失敗 - 表格: {table_name}, 錯誤: {e}")
        return jsonify({
            'success': False,
            'message': f'批量刪除失敗: {str(e)}'
        }), 500
        
    finally:
        if 'conn' in locals():
            conn.close()
