# Task 6: MVP Presenter Layer 前端整合需求

## Task 6 概述
實作 MVP Presenter Layer (business logic) - 作為 Model 和 View 之間的橋樑

## Task 6 詳細任務

### Task 6.1: 建立主要 PTS rename presenter
- **檔案**: `pts_rename_presenter.py` 作為核心業務邏輯控制器
- **功能**:
  - 處理上傳請求 (upload requests)
  - 處理檔案處理請求 (processing requests) 
  - 處理狀態查詢 (status queries)
  - 協調服務之間的互動並管理工作流程
- **需求**: 2.3, 2.4, 2.5, 8.1, 8.2

### Task 6.2: 實作核心 PTS rename 服務
- **檔案**: `pts_rename_service.py` 作為主要服務協調器
- **功能**:
  - 協調檔案處理、QC生成和目錄建立
  - 處理批次操作和結果最終化
  - 與 Dramatiq 任務系統整合
- **需求**: 5.4, 8.3, 8.4

## MVP 架構整合
- **Model**: 資料模型和實體 (Tasks 1-5 已完成)
- **View**: 用戶介面組件 (Task 7 - Flask templates/JavaScript)
- **Presenter**: 業務邏輯控制器 (Task 6 - 當前任務)

## 整合要求
- 與已完成的 Tasks 1-5 正確整合
- 與現有 Dramatiq 基礎設施協調
- 為 Task 7 Flask 網頁介面準備接口
- 使用現有的 `outlook.db` 資料庫
- 遵循檔案命名規範 (`pts_rename` 前綴)

## 監督檢查點
1. **檔案存在性**: 確保建立正確的檔案
2. **接口一致性**: 驗證與已有服務的整合
3. **業務邏輯完整性**: 檢查所有業務流程實作
4. **錯誤處理**: 確保適當的異常處理
5. **異步處理**: 驗證與 Dramatiq 的正確整合
6. **MVP 模式遵循**: 確保符合 MVP 架構原則

## 成功標準
- Presenter 層完整實作並測試通過
- 與現有服務層無縫整合
- 為 Flask 網頁介面提供清潔的接口
- 支援所有核心 PTS 處理功能
- 適當的錯誤處理和日誌記錄