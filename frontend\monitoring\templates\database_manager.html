<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫管理</title>
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('monitoring.static', filename='css/database.css') }}">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <!-- DataTables Responsive CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
    <style>
        .search-results-info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bee5eb;
            margin-top: 10px;
        }
        
        .sql-query-section {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .sql-query-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }
        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .search-tips {
            background-color: #e7f3ff;
            padding: 12px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .search-tips p {
            margin: 0;
            color: #495057;
            font-size: 14px;
        }
        .query-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: flex-end;
        }
        .query-container textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ffb74d;
            border-radius: 4px;
            font-size: 14px;
            font-family: monospace;
        }
        .query-container textarea:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 0 0.2rem rgba(255, 152, 0, 0.25);
        }
        .query-suggestions {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .query-suggestions h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-small:hover {
            background-color: #0056b3;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
            margin-top: 10px;
        }
        
        /* 額外的表格邊框強化 */
        table.dataTable.display {
            border-collapse: collapse !important;
        }
        
        table.dataTable.display thead th,
        table.dataTable.display tbody td {
            border: 1px solid #dee2e6 !important;
        }
        
        table.dataTable.display tbody tr {
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        /* 確保操作按鈕容器有適當的內邊距 */
        .action-buttons {
            white-space: nowrap;
            padding: 4px;
        }
        
        /* 改善標籤和狀態顯示 */
        .extraction-method-tag,
        .parse-status-tag,
        .vendor-code-tag {
            display: inline-block;
            margin: 1px;
            padding: 2px 6px;
            font-weight: 500;
            font-size: 0.75rem;
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 3px;
            background-color: #f8f9fa;
        }

        /* 狀態特定顏色 */
        .parse-status-parsed {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .parse-status-failed {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .extraction-method-llm {
            background-color: #e3f2fd;
            color: #1565c0;
            border-color: #bbdefb;
        }

        .extraction-method-traditional {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        /* 廠商代碼突出顯示 */
        .vendor-code-tag {
            font-weight: 600;
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        /* 響應式表格改進 */
        .dataTables_wrapper {
            overflow-x: auto;
        }

        /* 表格容器改進 */
        .data-container {
            max-width: 100%;
            overflow-x: auto;
        }

        /* 表格欄位內容截斷提示 */
        .truncated-content {
            position: relative;
            cursor: help;
        }

        .truncated-content:hover::after {
            content: "點擊查看完整內容";
            position: absolute;
            bottom: -25px;
            left: 0;
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        /* DataTables 響應式改進 */
        table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {
            background-color: #007bff;
        }

        /* 批量操作面板樣式 */
        .batch-actions-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .batch-actions-panel.hidden {
            display: none;
        }

        .batch-info {
            font-weight: 500;
            color: #495057;
        }

        .batch-buttons {
            display: flex;
            gap: 8px;
        }

        .batch-buttons .btn {
            padding: 6px 12px;
            font-size: 14px;
        }

        /* 欄位顯示控制器樣式 */
        .column-visibility-controller {
            position: relative;
            display: inline-block;
            margin-left: 10px;
        }

        /* 表格資訊區域的標題和控制器佈局 */
        .table-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .table-info-header h3 {
            margin: 0;
        }

        .table-info-header .column-visibility-controller {
            margin-left: 0;
        }

        .column-visibility-controller .btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            background-color: #fff;
            color: #495057;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .column-visibility-controller .btn:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
        }

        .column-visibility-controller .btn.active {
            background-color: #e9ecef;
            border-color: #6c757d;
        }

        .toggle-indicator {
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        .toggle-indicator.expanded {
            transform: rotate(180deg);
        }

        .column-visibility-panel {
            position: absolute;
            top: calc(100% + 5px);
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            min-width: 280px;
            z-index: 1000;
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .column-visibility-panel.collapsed {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            pointer-events: none;
        }

        .panel-header {
            padding: 12px 16px;
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-header h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .panel-actions {
            display: flex;
            gap: 8px;
        }

        .btn-link {
            background: none;
            border: none;
            color: #007bff;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .btn-link:hover {
            background-color: rgba(0, 123, 255, 0.1);
            text-decoration: none;
        }

        .panel-content {
            padding: 12px 16px;
            max-height: 300px;
            overflow-y: auto;
        }

        .column-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }

        .column-checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .column-checkbox-item:hover {
            background-color: #f8f9fa;
        }

        .column-checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .column-checkbox-item label {
            flex: 1;
            font-size: 13px;
            color: #495057;
            cursor: pointer;
            margin: 0;
        }

        .column-checkbox-item.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .column-category-header {
            grid-column: 1 / -1;
            font-weight: bold;
            color: #495057;
            margin-top: 8px;
            margin-bottom: 4px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 4px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 關閉面板的覆蓋層 */
        .column-visibility-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 999;
            background: transparent;
            display: none;
        }

        .column-visibility-overlay.active {
            display: block;
        }

        /* 確保表格在小螢幕上的可讀性 */
        @media (max-width: 1200px) {
            .data-container {
                padding: 0.5rem;
            }

            #data-table th,
            #data-table td {
                padding: 6px 8px;
                font-size: 14px;
            }

            .batch-actions-panel {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }

            .batch-buttons {
                justify-content: center;
            }

            .column-visibility-panel {
                right: -50px;
                min-width: 250px;
            }

            .column-checkboxes {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .table-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .column-visibility-controller {
                margin-left: 0;
                align-self: flex-start;
            }

            .column-visibility-panel {
                right: 0;
                left: 0;
                min-width: auto;
            }
            
            /* Epic-05: 響應式設計改進 */
            .kpi-cards {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }
            
            .charts-section {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }
            
            .metrics-container {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }
            
            .monitoring-panel {
                padding: 15px !important;
                margin-bottom: 20px !important;
            }
            
            .monitoring-panel h3 {
                font-size: 18px !important;
            }
            
            .kpi-value {
                font-size: 24px !important;
            }
        }
        
        @media (max-width: 1200px) {
            .charts-section {
                grid-template-columns: 1fr !important;
            }
            
            .kpi-cards {
                grid-template-columns: repeat(2, 1fr) !important;
            }
        }
        
        /* Epic-05: 圖表容器改進 */
        .chart-container canvas {
            max-width: 100%;
            height: auto;
        }
        
        /* Epic-05: 表格響應式改進 */
        .monitoring-panel table {
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .monitoring-panel table {
                font-size: 12px;
            }
            
            .monitoring-panel th,
            .monitoring-panel td {
                padding: 6px 4px !important;
            }
        }
        
        /* Epic-05: 狀態徽章樣式 */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 4px;
            line-height: 1;
        }
        
        .badge-warning {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .badge-success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .badge-danger {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-sm.btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-sm.btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-sm.btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-sm.btn-secondary:hover {
            background-color: #545b62;
        }
        
        /* 手動輸入對話框樣式 */
        .manual-input-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .manual-input-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .manual-input-form {
            display: grid;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .btn-save {
            background-color: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-save:hover {
            background-color: #45a049;
        }
        
        .btn-cancel {
            background-color: #f44336;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-cancel:hover {
            background-color: #d32f2f;
        }
        
        /* 表單提示和錯誤訊息樣式 */
        .form-hint {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
        }
        
        .error-message {
            margin-top: 4px;
            font-size: 12px;
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .form-group input.invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
        }
        
        .form-group input.valid {
            border-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
        }
    </style>
</head>
<body>
    <div class="db-manager-container">
        <!-- 頂部導航 -->
        <header class="db-header">
            <div class="header-left">
                <h1>🗄️ 資料庫管理</h1>
                <a href="/email/" class="btn btn-secondary">
                    <span class="btn-icon">◀</span>
                    <span class="btn-text">返回郵件收件夾</span>
                </a>
            </div>
            <div class="header-right">
                <div class="db-info">
                    <span class="info-item">資料庫路徑: <strong id="db-path">載入中...</strong></span>
                    <span class="info-item">大小: <strong id="db-size">計算中...</strong></span>
                </div>
            </div>
        </header>









        <!-- 表格選擇與搜尋 -->
        <div class="table-selector">
            <label for="table-select">選擇資料表：</label>
            <select id="table-select" class="form-select">
                <option value="">-- 請選擇資料表 --</option>
                <option value="emails">emails - 郵件</option>
                <option value="senders">senders - 寄件者</option>
                <option value="attachments">attachments - 附件</option>
                <option value="email_process_status">email_process_status - 處理狀態</option>
                <option value="email_download_status">email_download_status - 郵件下載狀態</option>
                <option value="email_download_retry_log">email_download_retry_log - 下載重試日誌</option>
            </select>
            <button id="refresh-btn" class="btn btn-primary">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">重新整理</span>
            </button>
            <button id="export-csv-btn" class="btn btn-secondary" disabled>
                <span class="btn-icon">📥</span>
                <span class="btn-text">匯出 CSV</span>
            </button>
        </div>

        <!-- 搜尋與查詢區域 -->
        <div class="sql-query-section">
            <h3>🔍 搜尋資料</h3>
            <div class="search-container">
                <input type="text" id="search-input" placeholder="輸入搜尋關鍵字（例如：tong、郵件主旨、廠商代碼...）" class="form-input">
                <button id="search-btn" class="btn btn-primary">搜尋</button>
                <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                <button id="select-all-search-btn" class="btn btn-info" style="margin-left: 10px;">
                    ☑️ 全選搜尋結果
                </button>
            </div>
            <div class="search-tips">
                <p>💡 <strong>搜尋提示：</strong>輸入「tong」可找到「<EMAIL>」，可搜尋寄件者、主旨、內容、廠商代碼等多個欄位</p>
            </div>
            <div id="search-results-info" class="search-results-info hidden">
                <span id="search-results-text"></span>
            </div>
            
            <!-- 隱藏的SQL查詢區域 -->
            <div style="display: none;">
                <textarea id="sql-query" placeholder="輸入 SELECT 查詢語句..." rows="3"></textarea>
                <button id="execute-query-btn" class="btn btn-primary">執行查詢</button>
            </div>
            
            <div class="query-suggestions">
                <h4>常用查詢範例：</h4>
                <div class="suggestion-buttons">
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, vendor_code, pd, lot, extraction_method FROM emails WHERE extraction_method = \'llm\' ORDER BY parsed_at DESC LIMIT 50')">LLM 解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, vendor_code, pd, lot, extraction_method FROM emails WHERE extraction_method = \'traditional\' ORDER BY parsed_at DESC LIMIT 50')">傳統解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, extraction_method, COUNT(*) as count FROM emails WHERE parse_status = \'parsed\' AND vendor_code IS NOT NULL GROUP BY vendor_code, extraction_method ORDER BY vendor_code, extraction_method')">各廠商解析方法統計</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, parse_error FROM emails WHERE parse_status = \'failed\' ORDER BY received_time DESC LIMIT 50')">解析失敗的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, COUNT(*) as total, COUNT(CASE WHEN parse_status = \'parsed\' THEN 1 END) as parsed, ROUND(COUNT(CASE WHEN parse_status = \'parsed\' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate FROM emails WHERE vendor_code IS NOT NULL GROUP BY vendor_code ORDER BY success_rate DESC')">廠商解析成功率</button>
                </div>
            </div>
            <div id="query-error" class="error-message hidden"></div>
        </div>

        <!-- 表格資訊 -->
        <div id="table-info" class="table-info hidden">
            <div class="table-info-header">
                <h3 id="table-name">資料表名稱</h3>
                <!-- 欄位顯示控制器 -->
                <div class="column-visibility-controller">
                    <button id="column-visibility-toggle" class="btn btn-outline-secondary" title="欄位顯示設定">
                        <span class="btn-icon">⚙</span>
                        <span class="btn-text">欄位設定</span>
                        <span class="toggle-indicator" id="toggle-indicator">▼</span>
                    </button>
                    <div id="column-visibility-panel" class="column-visibility-panel collapsed">
                        <div class="panel-header">
                            <h4>選擇顯示欄位</h4>
                            <div class="panel-actions">
                                <button id="show-all-columns" class="btn-link">全部顯示</button>
                                <button id="hide-all-columns" class="btn-link">全部隱藏</button>
                            </div>
                        </div>
                        <div class="panel-content">
                            <div id="column-checkboxes" class="column-checkboxes">
                                <!-- 動態生成的欄位複選框 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-stats">
                <span>總記錄數: <strong id="record-count">0</strong></span>
                <span>欄位數: <strong id="column-count">0</strong></span>
            </div>
        </div>

        <!-- 批量操作面板 -->
        <div id="batch-actions-panel" class="batch-actions-panel hidden">
            <div class="batch-info">
                <span id="selected-count">0</span> 筆記錄已選擇
            </div>
            <div class="batch-buttons">
                <button id="batch-delete-btn" class="btn btn-danger">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">批量刪除</span>
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary">
                    <span class="btn-icon">✖️</span>
                    <span class="btn-text">清除選擇</span>
                </button>
            </div>
        </div>

        <!-- 資料表格容器 -->
        <div id="data-container" class="data-container">
            <div id="loading" class="loading hidden">
                <div class="loading-spinner"></div>
                <p>載入資料中...</p>
            </div>
            <table id="data-table" class="display" style="width:100%">
                <!-- 動態生成 -->
            </table>
        </div>

        <!-- 詳情模態框 -->
        <div id="detail-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">記錄詳情</h3>
                    <button class="close-btn" onclick="closeDetailModal()">✕</button>
                </div>
                <div id="modal-body" class="modal-body">
                    <!-- 動態生成 -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeDetailModal()">關閉</button>
                </div>
            </div>
        </div>

        <!-- 欄位控制器覆蓋層 -->
        <div id="column-visibility-overlay" class="column-visibility-overlay"></div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <!-- DataTables Responsive -->
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <!-- Chart.js for Epic-05 monitoring charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定義 JS -->
    <script src="{{ url_for('monitoring.static', filename='js/database.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/database-extensions.js') }}"></script>

    <script>
        // 添加工具提示功能
        $(document).ready(function() {
            // 為截斷的內容添加點擊事件
            $(document).on('click', '.truncated-content', function() {
                const fullText = $(this).attr('title');
                if (fullText) {
                    alert(fullText);
                }
            });

            // 改進的懸停效果
            $(document).on('mouseenter', '.truncated-content', function() {
                $(this).css({
                    'background-color': '#f8f9fa',
                    'cursor': 'pointer'
                });
            }).on('mouseleave', '.truncated-content', function() {
                $(this).css({
                    'background-color': 'transparent',
                    'cursor': 'default'
                });
            });
        });
        
        // 郵件操作函數
        async function reParseEmail(emailId) {
            if (!confirm('確認要重新解析這封郵件嗎？')) return;
            
            try {
                const response = await fetch(`/email/api/parser/emails/${emailId}/reparse`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'dev-parser-key-12345'
                    }
                });
                
                const result = await response.json();
                if (result.success) {
                    alert('重新解析請求已提交！');
                    // 重新載入表格資料
                    if (window.databaseManager && window.databaseManager.currentTable) {
                        window.databaseManager.loadTableData(window.databaseManager.currentTable);
                    }
                } else {
                    alert('重新解析失敗: ' + result.message);
                }
            } catch (error) {
                alert('重新解析失敗: ' + error.message);
            }
        }
        
        async function manualInputEmail(emailId) {
            try {
                // 先獲取郵件資料
                const response = await fetch(`/email/api/${emailId}`);
                const emailData = await response.json();
                
                if (!emailData.success) {
                    alert('獲取郵件資料失敗');
                    return;
                }
                
                const email = emailData.data;
                
                // 創建對話框 HTML
                const dialogHtml = `
                    <div id="manual-input-dialog" class="manual-input-dialog">
                        <div class="manual-input-content">
                            <h3>手動輸入解析資料</h3>
                            <p><strong>郵件主題:</strong> ${email.subject}</p>
                            <p><strong>寄件者:</strong> ${email.sender}</p>
                            
                            <form class="manual-input-form" id="manual-input-form">
                                <div class="form-group">
                                    <label for="vendor">廠家 (Vendor) *</label>
                                    <select id="vendor" name="vendor" required>
                                        <option value="">請選擇廠家</option>
                                        <option value="JCET">JCET (致新)</option>
                                        <option value="GTK">GTK</option>
                                        <option value="ETD">ETD (Etrend)</option>
                                        <option value="LINGSEN">LINGSEN (凌昇)</option>
                                        <option value="XAHT">XAHT</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="pd">產品編號 (PD) *</label>
                                    <input type="text" id="pd" name="pd" placeholder="例如: PROD 123 或 G1625RD1U-F~NV" value="${email.pd || ''}" required>
                                    <small class="form-hint">任何非空格式都可以（包含空格）</small>
                                    <div id="pd-error" class="error-message" style="display: none;"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="lot">批次編號 (LOT) *</label>
                                    <input type="text" id="lot" name="lot" placeholder="例如: LOT 123 或 YHW0049.Y" value="${email.lot || ''}" required>
                                    <small class="form-hint">任何非空格式都可以（包含空格）</small>
                                    <div id="lot-error" class="error-message" style="display: none;"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="mo">製造訂單 (MO)</label>
                                    <input type="text" id="mo" name="mo" placeholder="例如: TS25063596.1" value="${email.mo || ''}">
                                    <small class="form-hint">選填：MO製令編號</small>
                                    <div id="mo-error" class="error-message" style="display: none;"></div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="yield">良率 (YIELD)</label>
                                    <input type="number" id="yield" name="yield" step="0.01" min="0" max="100" placeholder="例如: 97.38" value="${email.yield_value || ''}">
                                    <small class="form-hint">選填：0-100之間的數值</small>
                                    <div id="yield-error" class="error-message" style="display: none;"></div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="button" class="btn-cancel" onclick="hideManualInputDialog()">取消</button>
                                    <button type="submit" class="btn-save">儲存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;
                
                // 添加到頁面
                document.body.insertAdjacentHTML('beforeend', dialogHtml);
                
                // 設定廠家預設值
                const vendorSelect = document.getElementById('vendor');
                if (email.vendor_code || email.vendor) {
                    const vendorValue = email.vendor_code || email.vendor || '';
                    if (vendorSelect && vendorValue) {
                        vendorSelect.value = vendorValue;
                    }
                }
                
                // MO號碼多重來源檢查
                const moElement = document.getElementById('mo');
                const moValue = email.mo_number || email.mo || '';
                if (moElement && moValue) {
                    moElement.value = moValue;
                }
                
                // 良率多重來源檢查  
                const yieldElement = document.getElementById('yield');
                const yieldValue = email.yield_value || email.yield || '';
                if (yieldElement && yieldValue) {
                    const numericYield = String(yieldValue).replace('%', '').trim();
                    yieldElement.value = numericYield;
                }
                
                // LOT號碼多重來源檢查
                const lotElement = document.getElementById('lot');
                const lotValue = email.lot_number || email.lot || '';
                if (lotElement && lotValue) {
                    lotElement.value = lotValue;
                }
                
                // 綁定表單提交事件
                document.getElementById('manual-input-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    saveManualInput(emailId);
                });
                
                // 添加實時驗證
                setupFormValidation();
                
            } catch (error) {
                console.error('顯示手動輸入對話框失敗:', error);
                alert('顯示輸入對話框失敗');
            }
        }
        
        // 隱藏手動輸入對話框
        function hideManualInputDialog() {
            const dialog = document.getElementById('manual-input-dialog');
            if (dialog) {
                dialog.remove();
            }
        }
        
        // 設定表單實時驗證
        function setupFormValidation() {
            const pdInput = document.getElementById('pd');
            const lotInput = document.getElementById('lot');
            const moInput = document.getElementById('mo');
            const yieldInput = document.getElementById('yield');
            
            // 產品編號驗證
            if (pdInput) {
                pdInput.addEventListener('input', function() {
                    validateProductCode(this);
                });
                pdInput.addEventListener('blur', function() {
                    validateProductCode(this);
                });
            }
            
            // 批次編號驗證
            if (lotInput) {
                lotInput.addEventListener('input', function() {
                    validateLotNumber(this);
                });
                lotInput.addEventListener('blur', function() {
                    validateLotNumber(this);
                });
            }
            
            // MO號碼驗證
            if (moInput) {
                moInput.addEventListener('input', function() {
                    validateMoNumber(this);
                });
                moInput.addEventListener('blur', function() {
                    validateMoNumber(this);
                });
            }
            
            // 良率驗證
            if (yieldInput) {
                yieldInput.addEventListener('input', function() {
                    validateYieldValue(this);
                });
                yieldInput.addEventListener('blur', function() {
                    validateYieldValue(this);
                });
            }
        }
        
        // 驗證產品編號
        function validateProductCode(input) {
            const value = input.value.trim();
            const errorElement = document.getElementById('pd-error');
            
            // 清除之前的狀態
            input.classList.remove('valid', 'invalid');
            if (errorElement) errorElement.style.display = 'none';
            
            if (!value) {
                if (input.hasAttribute('required')) {
                    showValidationError(input, errorElement, '產品編號為必填欄位');
                    return false;
                }
                return true;
            }
            
            // 完全無格式限制：允許任何非空內容包含空格
            // 用戶要求："❌ PROD 123 (包含空格 - 不允許) 這個也可以"
            // 不需要任何格式驗證，只要非空即可
            
            showValidationSuccess(input);
            return true;
        }
        
        // 驗證批次編號
        function validateLotNumber(input) {
            const value = input.value.trim();
            const errorElement = document.getElementById('lot-error');
            
            input.classList.remove('valid', 'invalid');
            if (errorElement) errorElement.style.display = 'none';
            
            if (!value) {
                if (input.hasAttribute('required')) {
                    showValidationError(input, errorElement, '批次編號為必填欄位');
                    return false;
                }
                return true;
            }
            
            // 完全無格式限制：允許任何非空內容包含空格
            // 與產品編號使用相同的無限制政策
            // 不需要任何格式驗證，只要非空即可
            
            showValidationSuccess(input);
            return true;
        }
        
        // 驗證MO號碼
        function validateMoNumber(input) {
            const value = input.value.trim();
            const errorElement = document.getElementById('mo-error');
            
            input.classList.remove('valid', 'invalid');
            if (errorElement) errorElement.style.display = 'none';
            
            if (!value) {
                return true; // 選填欄位，空值有效
            }
            
            // 使用與產品編號相同的寬鬆驗證規則
            const moPattern = /^[^\s\x00-\x1F\x7F]+$/;
            if (!moPattern.test(value)) {
                showValidationError(input, errorElement, 'MO號碼不能包含空格或控制字元');
                return false;
            }
            
            showValidationSuccess(input);
            return true;
        }
        
        // 驗證良率值
        function validateYieldValue(input) {
            const value = input.value.trim();
            const errorElement = document.getElementById('yield-error');
            
            input.classList.remove('valid', 'invalid');
            if (errorElement) errorElement.style.display = 'none';
            
            if (!value) {
                return true; // 選填欄位，空值有效
            }
            
            const numValue = parseFloat(value);
            if (isNaN(numValue) || numValue < 0 || numValue > 100) {
                showValidationError(input, errorElement, '良率必須在 0-100 之間');
                return false;
            }
            
            showValidationSuccess(input);
            return true;
        }
        
        // 顯示驗證錯誤
        function showValidationError(input, errorElement, message) {
            input.classList.add('invalid');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }
        
        // 顯示驗證成功
        function showValidationSuccess(input) {
            input.classList.add('valid');
        }
        
        // 儲存手動輸入的資料
        async function saveManualInput(emailId) {
            // 執行前端驗證
            const pdInput = document.getElementById('pd');
            const lotInput = document.getElementById('lot');
            const moInput = document.getElementById('mo');
            const yieldInput = document.getElementById('yield');
            
            let isValid = true;
            
            // 驗證所有欄位
            if (pdInput && !validateProductCode(pdInput)) isValid = false;
            if (lotInput && !validateLotNumber(lotInput)) isValid = false;
            if (moInput && !validateMoNumber(moInput)) isValid = false;
            if (yieldInput && !validateYieldValue(yieldInput)) isValid = false;
            
            // 如果驗證失敗，不提交表單
            if (!isValid) {
                alert('請修正表單中的錯誤後再提交');
                return;
            }
            
            // 顯示載入狀態
            const submitBtn = document.querySelector('.btn-save');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '儲存中...';
            submitBtn.disabled = true;
            
            try {
                const form = document.getElementById('manual-input-form');
                const formData = new FormData(form);
                
                const data = {
                    vendor_code: formData.get('vendor'),
                    pd: formData.get('pd'),
                    lot: formData.get('lot'),
                    mo: formData.get('mo'),
                    yield_value: formData.get('yield') ? parseFloat(formData.get('yield')) : null
                };
                
                // 驗證必填欄位
                if (!data.vendor_code || !data.pd || !data.lot) {
                    alert('請填寫所有必填欄位（廠家、產品編號、批次編號）');
                    return;
                }
                
                console.log('發送手動輸入資料:', data);
                
                const response = await fetch(`/email/api/parser/emails/${emailId}/manual-input`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'dev-parser-key-12345'
                    },
                    body: JSON.stringify(data)
                });
                
                // 檢查 HTTP 狀態並獲取回應文字
                let responseText;
                if (!response.ok) {
                    responseText = await response.text();
                    console.error('HTTP 錯誤:', response.status, responseText);
                    alert(`儲存失敗: HTTP ${response.status} - ${responseText}`);
                    return;
                }
                
                // 獲取回應文字用於調試
                responseText = await response.text();
                console.log('原始 API 回應文字:', responseText);
                
                // 改進錯誤處理和回應解析
                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('API 回應物件:', result);
                } catch (jsonError) {
                    console.error('JSON 解析錯誤:', jsonError);
                    console.error('無法解析的回應文字:', responseText);
                    alert('儲存失敗: 伺服器回應格式錯誤 - ' + responseText.substring(0, 100));
                    return;
                }
                
                // 確保 result 存在且是物件
                if (!result || typeof result !== 'object') {
                    console.error('無效的 API 回應:', result);
                    alert('儲存失敗: 伺服器回應無效');
                    return;
                }
                
                if (result.success) {
                    alert('手動輸入資料儲存成功！');
                    hideManualInputDialog();
                    // 重新載入表格資料
                    if (window.databaseManager) {
                        window.databaseManager.loadTableData(window.databaseManager.currentTable);
                    }
                } else {
                    // 改進的錯誤訊息提取邏輯
                    let errorMsg = '未知錯誤';
                    
                    if (result.error && typeof result.error === 'string' && result.error.trim()) {
                        errorMsg = result.error.trim();
                    } else if (result.message && typeof result.message === 'string' && result.message.trim()) {
                        errorMsg = result.message.trim();
                    } else if (result.code) {
                        errorMsg = `錯誤代碼: ${result.code}`;
                    }
                    
                    console.error('API 錯誤詳細資訊:', {
                        result: result,
                        errorMsg: errorMsg,
                        resultType: typeof result,
                        errorField: result.error,
                        messageField: result.message,
                        codeField: result.code
                    });
                    
                    alert('儲存失敗: ' + errorMsg);
                }
            } catch (error) {
                console.error('儲存手動輸入資料失敗:', error);
                
                // 提供更具體的錯誤訊息
                let errorMessage = '儲存失敗';
                if (error.message) {
                    if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                        errorMessage = '網路連線錯誤，請檢查網路連線後重試';
                    } else if (error.message.includes('JSON')) {
                        errorMessage = '伺服器回應格式錯誤，請聯繫系統管理員';
                    } else {
                        errorMessage = '儲存失敗: ' + error.message;
                    }
                }
                
                alert(errorMessage);
            } finally {
                // 恢復按鈕狀態
                if (submitBtn) {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            }
        }
        
        async function processEmail(emailId, forceReprocess = false) {
            try {
                const response = await fetch(`/email/api/${emailId}/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ force: forceReprocess })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('郵件處理請求已提交！');
                    // 重新載入表格資料
                    if (window.databaseManager && window.databaseManager.currentTable) {
                        window.databaseManager.loadTableData(window.databaseManager.currentTable);
                    }
                } else {
                    // 檢查是否是已處理錯誤，提供強制處理選項
                    if (result.can_force_reprocess && !forceReprocess) {
                        const confirmed = confirm(
                            '郵件已處理過\n\n' + 
                            result.message + '\n\n是否要強制重新處理此郵件？'
                        );
                        
                        if (confirmed) {
                            // 遞歸調用，強制重新處理
                            return await processEmail(emailId, true);
                        }
                    } else {
                        alert('處理失敗: ' + result.message);
                    }
                }
                
            } catch (error) {
                console.error('處理郵件失敗:', error);
                alert('處理郵件失敗: ' + error.message);
            }
        }
        
        async function deleteEmail(emailId) {
            const confirmed = confirm('您確定要刪除這封郵件嗎？此操作無法撤銷。');
            if (!confirmed) return;
            
            try {
                const response = await fetch(`/email/api/${emailId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('郵件已刪除');
                    // 重新載入表格資料
                    if (window.databaseManager && window.databaseManager.currentTable) {
                        window.databaseManager.loadTableData(window.databaseManager.currentTable);
                    }
                } else {
                    alert('刪除失敗: ' + result.message);
                }
                
            } catch (error) {
                console.error('刪除郵件失敗:', error);
                alert('刪除郵件失敗: ' + error.message);
            }
        }
        
        async function viewEmailDetail(emailId) {
            try {
                const response = await fetch(`/email/api/${emailId}`);
                const result = await response.json();
                
                if (result.success) {
                    const email = result.data;
                    
                    // 創建詳情模態框
                    const modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.id = 'emailDetailModal';
                    modal.style.display = 'block';
                    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
                    modal.style.position = 'fixed';
                    modal.style.top = '0';
                    modal.style.left = '0';
                    modal.style.width = '100%';
                    modal.style.height = '100%';
                    modal.style.zIndex = '9999';
                    
                    modal.innerHTML = `
                        <div style="
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            max-width: 80%;
                            max-height: 80%;
                            overflow-y: auto;
                            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                        ">
                            <div style="border-bottom: 1px solid #ddd; padding-bottom: 15px; margin-bottom: 15px;">
                                <h3 style="margin: 0; color: #333;">郵件詳情</h3>
                                <button onclick="closeEmailDetail()" style="
                                    position: absolute;
                                    top: 15px;
                                    right: 15px;
                                    background: none;
                                    border: none;
                                    font-size: 24px;
                                    cursor: pointer;
                                    color: #666;
                                ">×</button>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <strong>寄件者:</strong> ${email.sender || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>主旨:</strong> ${email.subject || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>接收時間:</strong> ${email.received_time || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>廠商代碼:</strong> ${email.vendor_code || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>產品代碼 (PD):</strong> ${email.pd || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>MO:</strong> ${email.mo || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>LOT:</strong> ${email.lot || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>良率值:</strong> ${email.yield_value || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>解析狀態:</strong> ${email.parse_status || 'N/A'}
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>提取方法:</strong> ${email.extraction_method || 'N/A'}
                            </div>
                            
                            <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 20px;">
                                <strong>郵件內容:</strong>
                                <div style="
                                    background: #f8f9fa;
                                    padding: 15px;
                                    border-radius: 4px;
                                    margin-top: 10px;
                                    white-space: pre-wrap;
                                    max-height: 300px;
                                    overflow-y: auto;
                                    font-family: monospace;
                                    font-size: 12px;
                                    line-height: 1.4;
                                ">
                                    ${email.body || '無內容'}
                                </div>
                            </div>
                            
                            <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #ddd;">
                                <button onclick="closeEmailDetail()" style="
                                    background: #6c757d;
                                    color: white;
                                    border: none;
                                    padding: 8px 16px;
                                    border-radius: 4px;
                                    cursor: pointer;
                                ">關閉</button>
                            </div>
                        </div>
                    `;
                    
                    document.body.appendChild(modal);
                } else {
                    alert('獲取郵件詳情失敗: ' + result.message);
                }
                
            } catch (error) {
                console.error('獲取郵件詳情失敗:', error);
                alert('獲取郵件詳情失敗: ' + error.message);
            }
        }
        
        function closeEmailDetail() {
            const modal = document.getElementById('emailDetailModal');
            if (modal) {
                modal.remove();
            }
        }
    </script>
</body>
</html>