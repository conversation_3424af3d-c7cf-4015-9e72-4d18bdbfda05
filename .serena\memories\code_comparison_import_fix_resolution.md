# Code Comparison Script Import Fix Resolution

## Problem Summary
The `code_comparison.py` script was failing with the error "No module named 'psutil'" when trying to execute with the provided target folder path.

## Root Cause Analysis
1. **Missing psutil dependency**: The script's dependencies included psutil but it wasn't installed in the virtual environment
2. **Missing py7zr dependency**: The script needed py7zr to handle 7z file extraction but this wasn't installed
3. **Virtual environment activation**: The script needed to be run within the proper virtual environment (venv_win_3_11_9)

## Solutions Implemented

### 1. Dependencies Installation
```bash
# Install missing psutil dependency
"D:\project\python\outlook_summary\venv_win_3_11_9\Scripts\pip.exe" install psutil

# Install py7zr for 7z file handling
"D:\project\python\outlook_summary\venv_win_3_11_9\Scripts\pip.exe" install py7zr
```

### 2. Proper Virtual Environment Usage
The script must be executed using the virtual environment's Python interpreter:
```bash
"D:\project\python\outlook_summary\venv_win_3_11_9\Scripts\python.exe" code_comparison.py [args]
```

## Verification Results
- ✅ Script help output works correctly
- ✅ 7z file extraction works (extracted 21 files successfully)
- ✅ CTA file processing completed (4 files processed)
- ✅ All import statements resolve correctly

## Current Script Status
The script now runs successfully and processes files correctly, though it may report "no EQC BIN=1 data found" for certain datasets, which is expected behavior for data that doesn't contain BIN=1 records.

## Usage Instructions
```bash
# Basic usage with virtual environment
"D:\project\python\outlook_summary\venv_win_3_11_9\Scripts\python.exe" code_comparison.py ".\doc\G2536K81D-K(AD-CTAXP)\*********\" --verbose

# With Excel output
"D:\project\python\outlook_summary\venv_win_3_11_9\Scripts\python.exe" code_comparison.py ".\doc\G2536K81D-K(AD-CTAXP)\*********\" --excel --verbose
```

## Dependencies Added
- psutil==7.0.0 - System and process utilities
- py7zr==1.0.0 - 7z archive handling
  - Additional dependencies: texttable, brotli, pyzstd, pyppmd, pycryptodomex, pybcj, multivolumefile, inflate64

## Project Files Validated
- backend/shared/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py ✅
- backend/shared/infrastructure/adapters/excel/eqc/processors/eqc_standard_processor.py ✅