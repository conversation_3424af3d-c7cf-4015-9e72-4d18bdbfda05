/**
 * 資料庫管理前端邏輯 (核心功能)
 */

class DatabaseManager {
    constructor() {
        this.currentTable = null;
        this.dataTable = null;
        this.selectedRows = new Set();
        this.extensions = null;
        this.currentTableData = null;
        this.hiddenColumns = new Set([
            // 隱藏不必要的欄位，只保留 10 個核心欄位
            'body', 'message_id', 'is_read', 'has_attachments', 'attachment_count',
            'sender_display_name', 'is_processed', 'created_at',  // 重複欄位
            'download_completed_at', 'processing_completed_at',    // 時間戳欄位
            'llm_analysis_timestamp', 'parsed_at',                // 技術時間欄位
            'parse_error', 'llm_analysis_result',                 // 長文字欄位
            'updated_at',                                         // 不重要的時間欄位
            // 隱藏次要業務欄位，只保留最重要的
            'mo',                                                 // MO 相對不重要
            'extraction_method', 'llm_service_used',             // 技術細節
            'download_success', 'processing_success',            // 狀態可以通過解析狀態推斷
            'received_date'                                       // 與 received_time 重複
        ]);
        this.allColumns = [];
        this.defaultHiddenColumns = [
            // 精簡版：只保留 10 個最核心的欄位
            'body', 'message_id', 'is_read', 'has_attachments', 'attachment_count',
            'sender_display_name', 'is_processed', 'created_at', 'updated_at',
            'download_completed_at', 'processing_completed_at',
            'llm_analysis_timestamp', 'parsed_at', 'parse_error', 'llm_analysis_result',
            'mo', 'extraction_method', 'llm_service_used', 
            'download_success', 'processing_success', 'received_date'
        ];
        this.init();
    }

    init() {
        this.loadColumnVisibilitySettings();
        this.bindEvents();
        this.loadDatabaseInfo();
        // 初始化擴展功能
        this.extensions = new DatabaseManagerExtensions(this);
        // 自動載入 emails 表格
        setTimeout(() => {
            this.loadTableData('emails');
        }, 500);
    }

    bindEvents() {
        // 表格選擇
        $('#table-select').on('change', (e) => {
            const tableName = e.target.value;
            if (tableName) {
                this.loadTableData(tableName);
            } else {
                this.clearTableView();
            }
        });

        // 重新整理按鈕
        $('#refresh-btn').on('click', () => {
            if (this.currentTable) {
                this.loadTableData(this.currentTable);
            }
        });

        // 匯出 CSV 按鈕
        $('#export-csv-btn').on('click', () => {
            if (this.currentTable) {
                this.exportTableToCsv(this.currentTable);
            }
        });

        // 搜尋功能
        $('#search-btn').on('click', () => {
            this.searchData();
        });

        $('#search-input').on('keypress', (e) => {
            if (e.which === 13) { // Enter key
                this.searchData();
            }
        });

        // 清除搜尋
        $('#clear-search-btn').on('click', () => {
            $('#search-input').val('');
            if (this.currentTable) {
                this.loadTableData(this.currentTable);
            }
        });

        // 欄位顯示控制
        $('#column-visibility-toggle').on('click', () => {
            this.toggleColumnVisibilityPanel();
        });
        
        // 點擊覆蓋層關閉面板
        $('#column-visibility-overlay').on('click', () => {
            this.hideColumnVisibilityPanel();
        });
        
        // 全部顯示按鈕
        $(document).on('click', '#show-all-columns', () => {
            this.showAllColumns();
        });
        
        // 全部隱藏按鈕  
        $(document).on('click', '#hide-all-columns', () => {
            this.hideAllColumns();
        });
        
        // 欄位複選框變更
        $(document).on('change', '.column-checkbox', (e) => {
            this.toggleColumnVisibility(e.target.dataset.column, e.target.checked);
        });

        // 批次操作
        $('#batch-delete-btn').on('click', () => {
            this.batchDeleteRows();
        });
        
        // 清除選擇
        $('#clear-selection-btn').on('click', () => {
            this.clearAllSelections();
        });

        // 全選/取消全選
        $(document).on('change', '#select-all', (e) => {
            const isChecked = e.target.checked;
            $('.row-select').prop('checked', isChecked);
            this.updateSelectedRows();
        });

        // 單行選擇
        $(document).on('change', '.row-select', () => {
            this.updateSelectedRows();
        });

        // SQL 查詢執行
        $('#execute-query-btn').on('click', () => {
            this.executeCustomQuery();
        });
    }

    async loadDatabaseInfo() {
        try {
            const response = await fetch('/monitoring/api/database/info');
            const data = await response.json();
            
            if (data.success) {
                this.updateDatabaseInfo(data.data);
            }
        } catch (error) {
            console.error('載入資料庫資訊失敗:', error);
        }
    }

    updateDatabaseInfo(info) {
        $('#db-path').text(info.database_path);
        $('#db-size').text(info.database_size);
        
        // 更新表格選項
        const tableSelect = $('#table-select');
        tableSelect.empty().append('<option value="">選擇資料表</option>');
        
        info.tables.forEach(table => {
            tableSelect.append(`<option value="${table.name}">${table.name} (${table.count} 筆記錄)</option>`);
        });
        
        // 自動選擇 emails 表格
        tableSelect.val('emails');
    }

    async loadTableData(tableName, searchTerm = '') {
        try {
            this.currentTable = tableName;
            
            let url = `/monitoring/api/database/table/${tableName}`;
            if (searchTerm) {
                url += `?search=${encodeURIComponent(searchTerm)}`;
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.displayTableData(data.data);
                this.updateTableInfo(data.data);
            } else {
                this.showError('載入表格資料失敗: ' + data.message);
            }
        } catch (error) {
            console.error('載入表格資料失敗:', error);
            this.showError('載入表格資料失敗: ' + error.message);
        }
    }

    displayTableData(data) {
        const container = $('#data-container');
        
        if (!data.columns || data.columns.length === 0) {
            container.html('<p class="text-center">沒有資料可顯示</p>');
            return;
        }

        // 保存當前表格數據供其他方法使用
        this.currentTableData = data;
        this.allColumns = data.columns;
        let visibleColumns = data.columns.filter(col => !this.hiddenColumns.has(col));
        
        // 自訂欄位排序：廠商、pd、mo、lot、良率優先排列
        visibleColumns = this.sortColumnsWithPriority(visibleColumns);
        
        // 顯示表格資訊區域和欄位控制按鈕
        $('#table-info').removeClass('hidden');
        
        let html = `
            <div class="table-responsive">
                <table id="data-table" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th><input type="checkbox" id="select-all"></th>
                            ${visibleColumns.map(col => `<th>${this.getColumnDisplayName(col)}</th>`).join('')}
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.rows.forEach((row, index) => {
            html += '<tr>';
            // 根據表格類型設置不同的 data 屬性
            if (this.currentTable === 'emails') {
                html += `<td><input type="checkbox" class="row-select" data-row-index="${index}" data-email-id="${row.id}"></td>`;
            } else {
                html += `<td><input type="checkbox" class="row-select" data-row-index="${index}" data-row-id="${row.id}"></td>`;
            }
            
            visibleColumns.forEach(col => {
                let value = row[col];
                
                // 特殊處理狀態欄位和格式化
                if (this.isBooleanSuccessColumn(col)) {
                    value = this.formatBooleanSuccess(value);
                } else if (col === 'parse_status') {
                    value = this.formatParseStatus(value);
                } else if (col === 'extraction_method') {
                    value = this.formatExtractionMethod(value);
                } else if (col === 'vendor_code' && value) {
                    value = `<span class="vendor-code-tag">${value}</span>`;
                } else if (value === null || value === undefined) {
                    value = '<span class="text-muted">NULL</span>';
                } else if (typeof value === 'string' && value.length > 100) {
                    value = value.substring(0, 100) + '...';
                }
                
                html += `<td>${value}</td>`;
            });
            
            html += `
                <td class="action-buttons">
                    <button class="btn btn-sm btn-success" onclick="reParseEmail(${row.id})" title="重新解析">
                        🔄 重新解析
                    </button>
                    <button class="btn btn-sm btn-info" onclick="manualInputEmail(${row.id})" title="手動輸入">
                        ✏️ 手動輸入
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="processEmail(${row.id})" title="處理郵件">
                        🚀 處理
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="viewEmailDetail(${row.id})" title="查看詳情">
                        👁️ 詳情
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteEmail(${row.id})" title="刪除郵件">
                        🗑️ 刪除
                    </button>
                </td>
            `;
            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.html(html);
        this.selectedRows.clear();
    }

    isBooleanSuccessColumn(columnName) {
        return columnName === 'download_success' || columnName === 'processing_success';
    }

    formatBooleanSuccess(value) {
        if (value === 1 || value === true || value === 'true') {
            return '<span class="badge bg-success">成功</span>';
        } else if (value === 0 || value === false || value === 'false') {
            return '<span class="badge bg-danger">失敗</span>';
        } else {
            return '<span class="badge bg-secondary">未知</span>';
        }
    }

    sortColumnsWithPriority(columns) {
        // 定義完整的欄位順序：ID → 寄件者 → 主旨 → 接收時間 → 廠商代碼 → PD → MO → LOT → 良率值
        const fullOrder = [
            'id', 'sender', 'subject', 'received_time', 
            'vendor_code', 'pd', 'mo', 'lot', 'yield_value'
        ];
        
        // 分離已定義順序的欄位和其他欄位
        const orderedColumns = [];
        const otherColumns = [];
        
        columns.forEach(col => {
            if (fullOrder.includes(col)) {
                orderedColumns.push(col);
            } else {
                otherColumns.push(col);
            }
        });
        
        // 按照定義的完整順序排列
        orderedColumns.sort((a, b) => {
            return fullOrder.indexOf(a) - fullOrder.indexOf(b);
        });
        
        // 合併：有序欄位 + 其他欄位
        return [...orderedColumns, ...otherColumns];
    }

    getColumnDisplayName(columnName) {
        const displayNames = {
            // 基本資訊
            'id': 'ID',
            'sender': '寄件者',
            'sender_display_name': '寄件者顯示名稱',
            'subject': '主旨',
            'received_time': '接收時間',
            'received_date': '接收日期',
            'created_at': '建立時間',
            'updated_at': '更新時間',
            
            // 狀態資訊
            'download_success': '下載狀態',
            'processing_success': '處理狀態',
            'is_processed': '是否處理',
            'parse_status': '解析狀態',
            
            // 業務資訊
            'vendor_code': '廠商代碼',
            'pd': 'PD',
            'lot': 'LOT',
            'mo': 'MO',
            'yield_value': '良率值',
            
            // 時間資訊
            'download_completed_at': '下載完成時間',
            'processing_completed_at': '處理完成時間',
            'parsed_at': '解析時間',
            'llm_analysis_timestamp': 'AI分析時間',
            
            // 技術資訊
            'extraction_method': '提取方法',
            'llm_service_used': 'AI服務',
            'llm_analysis_result': 'AI分析結果',
            'parse_error': '解析錯誤',
            
            // 隱藏欄位
            'body': '郵件內容',
            'message_id': '郵件ID',
            'is_read': '是否已讀',
            'has_attachments': '有附件',
            'attachment_count': '附件數量'
        };
        return displayNames[columnName] || columnName;
    }

    updateTableInfo(data) {
        $('#table-name').text(this.currentTable);
        $('#record-count').text(data.rows.length);
        $('#column-count').text(data.columns.length);
    }

    searchData() {
        const searchTerm = $('#search-input').val().trim();
        if (this.currentTable) {
            this.loadTableData(this.currentTable, searchTerm);
        }
    }

    clearTableView() {
        $('#data-container').html('<p class="text-center text-muted">請選擇一個資料表</p>');
        $('#table-name').text('-');
        $('#record-count').text('0');
        $('#column-count').text('0');
        this.currentTable = null;
    }

    showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('#data-container').prepend(alertHtml);
    }

    updateSelectedRows() {
        this.selectedRows.clear();
        $('.row-select:checked').each((index, element) => {
            this.selectedRows.add(parseInt($(element).data('row-index')));
        });
        
        $('#batch-delete-btn').prop('disabled', this.selectedRows.size === 0);
        
        // 更新選擇計數和顯示批量操作面板
        const selectedCount = this.selectedRows.size;
        $('#selected-count').text(selectedCount);
        
        if (selectedCount > 0) {
            $('#batch-actions-panel').removeClass('hidden');
        } else {
            $('#batch-actions-panel').addClass('hidden');
        }
    }
    
    async batchDeleteRows() {
        if (this.selectedRows.size === 0) {
            alert('請先選擇要刪除的記錄');
            return;
        }
        
        // 根據表格類型確定刪除的內容描述
        const itemType = this.currentTable === 'emails' ? '郵件' : '記錄';
        const confirmed = confirm(`您確定要刪除選中的 ${this.selectedRows.size} 筆${itemType}嗎？此操作無法撤銷。`);
        if (!confirmed) return;
        
        try {
            // 獲取選中行的ID
            const selectedIds = [];
            $('.row-select:checked').each((index, element) => {
                const rowId = $(element).data('email-id') || $(element).data('row-id');
                if (rowId) {
                    selectedIds.push(rowId);
                }
            });
            
            if (selectedIds.length === 0) {
                alert('無法獲取選中記錄的ID');
                return;
            }
            
            let response, result;
            
            // 根據表格類型使用不同的 API 端點
            if (this.currentTable === 'emails') {
                // 郵件表格使用郵件 API
                response = await fetch('/email/api/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email_ids: selectedIds })
                });
            } else {
                // 其他表格使用通用資料庫 API
                response = await fetch(`/monitoring/api/database/table/${this.currentTable}/batch-delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ids: selectedIds })
                });
            }
            
            result = await response.json();
            
            if (result.success) {
                const deletedCount = result.deleted_count || result.affected_rows || selectedIds.length;
                alert(`成功刪除 ${deletedCount} 筆${itemType}`);
                // 重新載入表格
                this.loadTableData(this.currentTable);
                // 清空選擇
                this.clearAllSelections();
            } else {
                alert('批量刪除失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('批量刪除失敗:', error);
            alert('批量刪除失敗: ' + error.message);
        }
    }
    
    clearAllSelections() {
        // 取消所有複選框
        $('.row-select').prop('checked', false);
        $('#select-all').prop('checked', false);
        
        // 清空選擇集合
        this.selectedRows.clear();
        
        // 隱藏批量操作面板
        $('#batch-actions-panel').addClass('hidden');
        $('#selected-count').text('0');
    }

    async exportTableToCsv(tableName) {
        try {
            const response = await fetch(`/monitoring/api/database/table/${tableName}?format=csv`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${tableName}_export.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('匯出 CSV 失敗:', error);
            this.showError('匯出 CSV 失敗: ' + error.message);
        }
    }

    loadColumnVisibilitySettings() {
        const saved = localStorage.getItem('db_hidden_columns');
        if (saved) {
            this.hiddenColumns = new Set(JSON.parse(saved));
        } else {
            // 如果沒有儲存設定，使用預設隱藏欄位
            this.hiddenColumns = new Set(this.defaultHiddenColumns);
        }
        
        // 強制更新隱藏欄位為精簡版本（只保留 10 個核心欄位）
        const version = localStorage.getItem('column_config_version');
        if (version !== '3.0-minimal') {
            this.hiddenColumns = new Set(this.defaultHiddenColumns);
            this.saveColumnVisibilitySettings();
            localStorage.setItem('column_config_version', '3.0-minimal');
        }
    }

    saveColumnVisibilitySettings() {
        localStorage.setItem('db_hidden_columns', JSON.stringify([...this.hiddenColumns]));
    }

    toggleColumnVisibilityPanel() {
        const panel = $('#column-visibility-panel');
        const overlay = $('#column-visibility-overlay');
        const indicator = $('#toggle-indicator');
        
        if (panel.hasClass('collapsed')) {
            // 顯示面板
            this.generateColumnCheckboxes();
            panel.removeClass('collapsed');
            overlay.addClass('active');
            indicator.addClass('expanded');
        } else {
            // 隱藏面板
            this.hideColumnVisibilityPanel();
        }
    }
    
    hideColumnVisibilityPanel() {
        const panel = $('#column-visibility-panel');
        const overlay = $('#column-visibility-overlay');
        const indicator = $('#toggle-indicator');
        
        panel.addClass('collapsed');
        overlay.removeClass('active');
        indicator.removeClass('expanded');
    }
    
    generateColumnCheckboxes() {
        if (!this.allColumns || this.allColumns.length === 0) {
            return;
        }
        
        const container = $('#column-checkboxes');
        container.empty();
        
        this.allColumns.forEach(column => {
            const isVisible = !this.hiddenColumns.has(column);
            const displayName = this.getColumnDisplayName(column);
            
            const checkboxHtml = `
                <div class="column-checkbox-item">
                    <input type="checkbox" class="column-checkbox" 
                           data-column="${column}" 
                           ${isVisible ? 'checked' : ''}>
                    <label>${displayName}</label>
                </div>
            `;
            container.append(checkboxHtml);
        });
    }
    
    showAllColumns() {
        this.hiddenColumns.clear();
        this.saveColumnVisibilitySettings();
        this.generateColumnCheckboxes();
        if (this.currentTable) {
            this.loadTableData(this.currentTable);
        }
    }
    
    hideAllColumns() {
        // 保留一些基本欄位，不能全部隱藏
        this.hiddenColumns = new Set(this.allColumns.filter(col => !['id', 'sender', 'subject'].includes(col)));
        this.saveColumnVisibilitySettings();
        this.generateColumnCheckboxes();
        if (this.currentTable) {
            this.loadTableData(this.currentTable);
        }
    }
    
    toggleColumnVisibility(column, isVisible) {
        if (isVisible) {
            this.hiddenColumns.delete(column);
        } else {
            this.hiddenColumns.add(column);
        }
        this.saveColumnVisibilitySettings();
        if (this.currentTable) {
            this.loadTableData(this.currentTable);
        }
    }
    
    // 格式化解析狀態
    formatParseStatus(status) {
        const statusMap = {
            'parsed': '<span class="parse-status-tag parse-status-parsed">✅ 已解析</span>',
            'failed': '<span class="parse-status-tag parse-status-failed">❌ 失敗</span>',
            'pending': '<span class="parse-status-tag parse-status-pending">⏳ 待解析</span>',
            'processing': '<span class="parse-status-tag parse-status-processing">🔄 解析中</span>'
        };
        return statusMap[status] || status;
    }
    
    // 格式化提取方法
    formatExtractionMethod(method) {
        const methodMap = {
            'llm': '<span class="extraction-method-tag extraction-method-llm">🤖 AI解析</span>',
            'traditional': '<span class="extraction-method-tag extraction-method-traditional">📋 傳統解析</span>',
            'manual': '<span class="extraction-method-tag extraction-method-manual">✏️ 手動輸入</span>'
        };
        return methodMap[method] || (method ? `<span class="extraction-method-tag">${method}</span>` : 'NULL');
    }
}

// 全域函數 - 常用查詢範例
function setQuery(query) {
    $('#sql-query').val(query);
    // 顯示SQL查詢區域
    $('#sql-query').parent().show();
    // 自動執行查詢
    $('#execute-query-btn').click();
}

// 頁面載入完成後初始化
$(document).ready(function() {
    window.databaseManager = new DatabaseManager();
});
