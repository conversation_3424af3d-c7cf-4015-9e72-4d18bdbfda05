# 監控整合修復 - PTS Rename 資料庫

## 問題分析
PTS Renamer 改用專用資料庫 `data/pts_rename.db` 後，監控系統可能未包含新資料庫的健康檢查。

## 用戶確認
- **項目 2 (備份策略)**: 用戶認為不需要管 - PTS 是新功能，可能沒有需要備份的歷史資料
- **項目 3 (資料遷移)**: 用戶認為不需要管 - 確認沒有需要從 outlook.db 遷移的 PTS 資料
- **項目 1 (監控整合)**: 主要需要修復的問題

## 監控整合需求
需要更新監控系統以包含 `data/pts_rename.db`:

1. **資料庫健康檢查**: 確保 PTS 資料庫連接狀態被監控
2. **檔案系統監控**: 檢查 `pts_rename.db` 檔案大小和可訪問性  
3. **儀表板整合**: 在監控儀表板中顯示 PTS 資料庫狀態
4. **警報配置**: 當 PTS 資料庫出現問題時發送警報

## 相關檔案
需要檢查和可能更新的監控相關檔案:
- `backend/monitoring/` 相關檔案
- `frontend/monitoring/` 相關檔案
- 監控配置檔案
- 儀表板設定

## 目標
確保監控系統完全支援 PTS Renamer 的專用資料庫，提供與其他系統組件相同級別的監控和健康檢查。