#!/usr/bin/env python3
"""
修復 email_download_status 表格結構
添加缺失的欄位：download_progress, downloaded_bytes, file_size_bytes, updated_at
"""

import sqlite3
import sys
from datetime import datetime
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def fix_email_download_status_schema(db_path: str):
    """修復 email_download_status 表格結構"""
    
    print(f"[FIX] 開始修復 email_download_status 表格結構...")
    print(f"[INFO] 資料庫路徑: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查當前表格結構
        print("\n[INFO] 檢查當前表格結構...")
        cursor.execute("PRAGMA table_info(email_download_status)")
        existing_columns = {row[1]: row for row in cursor.fetchall()}
        
        print(f"現有欄位數量: {len(existing_columns)}")
        for col_name in existing_columns.keys():
            print(f"  - {col_name}")
        
        # 需要添加的欄位
        missing_columns = [
            ("download_progress", "REAL DEFAULT 0.0 NOT NULL"),
            ("downloaded_bytes", "BIGINT DEFAULT 0 NOT NULL"),
            ("file_size_bytes", "BIGINT"),
            ("updated_at", "DATETIME")  # 先不設置 NOT NULL，後面再更新
        ]
        
        # 檢查哪些欄位缺失
        columns_to_add = []
        for col_name, col_def in missing_columns:
            if col_name not in existing_columns:
                columns_to_add.append((col_name, col_def))
                print(f"[MISSING] 缺失欄位: {col_name}")
            else:
                print(f"[OK] 欄位已存在: {col_name}")
        
        if not columns_to_add:
            print("\n[SUCCESS] 所有欄位都已存在，無需修復！")
            return True
        
        # 備份資料庫
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"\n[BACKUP] 創建備份: {backup_path}")
        
        with open(db_path, 'rb') as src, open(backup_path, 'wb') as dst:
            dst.write(src.read())
        
        # 添加缺失的欄位
        print(f"\n[ADD] 添加 {len(columns_to_add)} 個缺失欄位...")
        
        for col_name, col_def in columns_to_add:
            try:
                sql = f"ALTER TABLE email_download_status ADD COLUMN {col_name} {col_def}"
                print(f"執行: {sql}")
                cursor.execute(sql)
                print(f"[OK] 成功添加欄位: {col_name}")
            except sqlite3.Error as e:
                print(f"[ERROR] 添加欄位 {col_name} 失敗: {e}")
                raise
        
        # 更新現有記錄的 updated_at
        if any(col[0] == 'updated_at' for col in columns_to_add):
            print("\n[UPDATE] 更新現有記錄的 updated_at 欄位...")
            # 先設置所有現有記錄的 updated_at 為適當的時間戳
            cursor.execute("""
                UPDATE email_download_status 
                SET updated_at = COALESCE(completed_at, started_at, created_at, datetime('now'))
                WHERE updated_at IS NULL
            """)
            updated_rows = cursor.rowcount
            print(f"[OK] 更新了 {updated_rows} 筆記錄的 updated_at")
            
            # 然後將所有 NULL 值設為當前時間戳
            cursor.execute("""
                UPDATE email_download_status 
                SET updated_at = datetime('now')
                WHERE updated_at IS NULL
            """)
            additional_updates = cursor.rowcount
            if additional_updates > 0:
                print(f"[OK] 額外更新了 {additional_updates} 筆 NULL 記錄")
        
        # 提交更改
        conn.commit()
        
        # 驗證結果
        print("\n[VERIFY] 驗證修復結果...")
        cursor.execute("PRAGMA table_info(email_download_status)")
        final_columns = {row[1]: row for row in cursor.fetchall()}
        
        print(f"修復後欄位數量: {len(final_columns)}")
        
        # 檢查所有必需欄位是否存在
        all_required_columns = [col[0] for col in missing_columns]
        missing_after_fix = [col for col in all_required_columns if col not in final_columns]
        
        if missing_after_fix:
            print(f"[ERROR] 修復後仍缺失欄位: {missing_after_fix}")
            return False
        
        print("[OK] 所有必需欄位已存在！")
        
        # 測試資料庫查詢
        print("\n[TEST] 測試資料庫查詢...")
        try:
            cursor.execute("""
                SELECT id, email_id, status, download_progress, downloaded_bytes, 
                       file_size_bytes, updated_at 
                FROM email_download_status LIMIT 1
            """)
            test_result = cursor.fetchone()
            if test_result:
                print(f"[OK] 查詢測試成功: 找到 {len(test_result)} 個欄位")
            else:
                print("[INFO] 表格為空，但查詢結構正確")
        except sqlite3.Error as e:
            print(f"[ERROR] 查詢測試失敗: {e}")
            return False
        
        print(f"\n[SUCCESS] 資料庫結構修復完成！")
        print(f"[BACKUP] 備份檔案: {backup_path}")
        return True
        
    except Exception as e:
        print(f"[ERROR] 修復過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函數"""
    db_path = project_root / "email_inbox.db"
    
    if not db_path.exists():
        print(f"[ERROR] 資料庫檔案不存在: {db_path}")
        return False
    
    success = fix_email_download_status_schema(str(db_path))
    
    if success:
        print("\n[SUCCESS] 修復完成！現在可以正常使用批量刪除功能了。")
        return True
    else:
        print("\n[FAILED] 修復失敗！請檢查錯誤訊息並手動修復。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)