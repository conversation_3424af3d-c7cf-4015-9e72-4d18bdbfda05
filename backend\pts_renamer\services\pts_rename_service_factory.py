"""
PTS Renamer Service Factory

This module provides factory functions for creating properly configured
PTS Renamer services with all dependencies properly injected.
"""

from typing import Optional
from pathlib import Path

from loguru import logger

# Import services
from .pts_rename_service import PTSRenameService
from .pts_rename_presenter import PTSRenamePresenter
from .pts_rename_processor import PTSRenameProcessor
from .pts_rename_qc_generator import PTSQCGenerator
from .pts_rename_directory_manager import PTSDirectoryManager
from .pts_rename_upload_service import PTSRenameUploadService
from .pts_rename_download_service import PTSRenameDownloadService
from .pts_rename_task_queue import PTSRenameTaskQueue, create_pts_rename_task_queue

# Import repository
from ..repositories.pts_rename_sql_repository import create_pts_rename_repository

# Import models
from ..models.pts_rename_models import PTSRenameConfig


class PTSRenameServiceFactory:
    """
    Factory for creating PTS Renamer service instances
    
    This factory handles dependency injection and proper service configuration
    according to the MVP architecture pattern.
    """
    
    def __init__(self, config: Optional[PTSRenameConfig] = None, database_path: Optional[str] = None):
        """
        Initialize service factory
        
        Args:
            config: PTS Renamer configuration
            database_path: Path to database file
        """
        self.config = config or PTSRenameConfig()
        self.database_path = database_path or "outlook.db"
        self._repository = None
        self._task_queue = None
        self._services_cache = {}
    
    def create_presenter(self) -> PTSRenamePresenter:
        """
        Create configured PTS Rename Presenter
        
        Returns:
            Configured presenter instance
        """
        if 'presenter' not in self._services_cache:
            logger.info("Creating PTS Rename Presenter")
            
            # Create dependencies
            pts_service = self.create_service()
            upload_service = self.create_upload_service()
            
            # Create presenter
            presenter = PTSRenamePresenter(
                pts_service=pts_service,
                upload_service=upload_service,
                logger=logger
            )
            
            self._services_cache['presenter'] = presenter
            logger.info("PTS Rename Presenter created successfully")
        
        return self._services_cache['presenter']
    
    def create_service(self) -> PTSRenameService:
        """
        Create configured PTS Rename Service
        
        Returns:
            Configured service instance
        """
        if 'service' not in self._services_cache:
            logger.info("Creating PTS Rename Service")
            
            # Create dependencies
            repository = self.get_repository()
            processor = self.create_processor()
            qc_generator = self.create_qc_generator()
            directory_manager = self.create_directory_manager()
            download_service = self.create_download_service()
            task_queue = self.get_task_queue()
            
            # Create service
            service = PTSRenameService(
                repository=repository,
                processor=processor,
                qc_generator=qc_generator,
                directory_manager=directory_manager,
                download_service=download_service,
                task_queue=task_queue,
                logger=logger
            )
            
            self._services_cache['service'] = service
            logger.info("PTS Rename Service created successfully")
        
        return self._services_cache['service']
    
    def create_processor(self) -> PTSRenameProcessor:
        """Create PTS file processor"""
        if 'processor' not in self._services_cache:
            logger.debug("Creating PTS Rename Processor")
            self._services_cache['processor'] = PTSRenameProcessor()
        return self._services_cache['processor']
    
    def create_qc_generator(self) -> PTSQCGenerator:
        """Create QC file generator"""
        if 'qc_generator' not in self._services_cache:
            logger.debug("Creating PTS QC Generator")
            self._services_cache['qc_generator'] = PTSQCGenerator()
        return self._services_cache['qc_generator']
    
    def create_directory_manager(self) -> PTSDirectoryManager:
        """Create directory manager"""
        if 'directory_manager' not in self._services_cache:
            logger.debug("Creating PTS Directory Manager")
            self._services_cache['directory_manager'] = PTSDirectoryManager()
        return self._services_cache['directory_manager']
    
    def create_upload_service(self) -> PTSRenameUploadService:
        """Create upload service"""
        if 'upload_service' not in self._services_cache:
            logger.debug("Creating PTS Upload Service")
            repository = self.get_repository()
            self._services_cache['upload_service'] = PTSRenameUploadService(
                repository=repository,
                config=self.config
            )
        return self._services_cache['upload_service']
    
    def create_download_service(self) -> PTSRenameDownloadService:
        """Create download service"""
        if 'download_service' not in self._services_cache:
            logger.debug("Creating PTS Download Service")
            repository = self.get_repository()
            self._services_cache['download_service'] = PTSRenameDownloadService(
                repository=repository,
                config=self.config
            )
        return self._services_cache['download_service']
    
    def get_repository(self):
        """Get repository instance (singleton)"""
        if self._repository is None:
            logger.debug(f"Creating PTS Rename Repository for database: {self.database_path}")
            self._repository = create_pts_rename_repository(self.database_path)
        return self._repository
    
    def get_task_queue(self) -> PTSRenameTaskQueue:
        """Get task queue instance (singleton)"""
        if self._task_queue is None:
            logger.debug("Creating PTS Rename Task Queue")
            self._task_queue = create_pts_rename_task_queue()
        return self._task_queue
    
    def cleanup(self):
        """Clean up factory resources"""
        logger.debug("Cleaning up PTS Rename Service Factory")
        self._services_cache.clear()
        if self._repository:
            # Repository cleanup if needed
            pass
        if self._task_queue:
            # Task queue cleanup if needed
            pass


class PTSRenameServiceError(Exception):
    """Exception raised by service factory operations"""
    
    def __init__(self, message: str, service_name: str = None):
        super().__init__(message)
        self.service_name = service_name


# Global factory instance for easy access
_global_factory: Optional[PTSRenameServiceFactory] = None


def get_global_factory(config: Optional[PTSRenameConfig] = None, 
                      database_path: Optional[str] = None) -> PTSRenameServiceFactory:
    """
    Get global factory instance (singleton)
    
    Args:
        config: PTS Renamer configuration (only used for first call)
        database_path: Database path (only used for first call)
        
    Returns:
        Global factory instance
    """
    global _global_factory
    if _global_factory is None:
        logger.info("Initializing global PTS Rename Service Factory")
        _global_factory = PTSRenameServiceFactory(config, database_path)
    return _global_factory


def create_pts_rename_presenter(config: Optional[PTSRenameConfig] = None,
                               database_path: Optional[str] = None) -> PTSRenamePresenter:
    """
    Convenience function to create a fully configured presenter
    
    Args:
        config: PTS Renamer configuration
        database_path: Database path
        
    Returns:
        Configured presenter instance
    """
    factory = PTSRenameServiceFactory(config, database_path)
    return factory.create_presenter()


def create_pts_rename_service(config: Optional[PTSRenameConfig] = None,
                             database_path: Optional[str] = None) -> PTSRenameService:
    """
    Convenience function to create a fully configured service
    
    Args:
        config: PTS Renamer configuration
        database_path: Database path
        
    Returns:
        Configured service instance
    """
    factory = PTSRenameServiceFactory(config, database_path)
    return factory.create_service()


# For backward compatibility and easy imports
def get_pts_rename_presenter() -> PTSRenamePresenter:
    """Get presenter using global factory"""
    return get_global_factory().create_presenter()


def get_pts_rename_service() -> PTSRenameService:
    """Get service using global factory"""
    return get_global_factory().create_service()


def initialize_pts_renamer_services(config: Optional[PTSRenameConfig] = None,
                                   database_path: Optional[str] = None):
    """
    Initialize PTS Renamer services for application startup
    
    Args:
        config: PTS Renamer configuration
        database_path: Database path
    """
    logger.info("Initializing PTS Renamer services")
    factory = get_global_factory(config, database_path)
    
    # Pre-create core services to ensure they're properly initialized
    try:
        factory.create_presenter()
        factory.create_service()
        logger.info("PTS Renamer services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize PTS Renamer services: {e}")
        raise PTSRenameServiceError(f"Service initialization failed: {e}")


def cleanup_pts_renamer_services():
    """Clean up PTS Renamer services on application shutdown"""
    global _global_factory
    if _global_factory:
        logger.info("Cleaning up PTS Renamer services")
        _global_factory.cleanup()
        _global_factory = None