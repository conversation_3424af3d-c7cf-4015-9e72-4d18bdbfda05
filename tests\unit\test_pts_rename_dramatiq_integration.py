"""
Unit Tests for PTS Rename Dramatiq Integration

Tests the integration of PTS file renaming operations with the Dramatiq
task queue system, including job processing, status tracking, and monitoring.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Import test fixtures
from tests.conftest import pytest_asyncio_fixture

# Import modules under test
from backend.pts_renamer.services.pts_rename_dramatiq_integration import (
    process_pts_rename_job_task,
    pts_file_compression_task,
    pts_cleanup_task
)
from backend.pts_renamer.services.pts_rename_job_monitor import (
    PTSRenameJobMonitor,
    PTSJobPhase,
    PTSJobMetrics,
    PTSJobStep
)
from backend.pts_renamer.services.pts_rename_task_service import PTSRenameTaskService
from backend.pts_renamer.services.pts_rename_monitoring_integration import PTSMonitoringCollector

# Import models
from backend.pts_renamer.models.pts_rename_entities import (
    PTSProcessingJob,
    PTSFile,
    PTSOperationType
)
from backend.pts_renamer.models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSProcessingStatus
)


class TestPTSRenameJobMonitor:
    """Test PTS job monitoring functionality"""
    
    @pytest.fixture
    def mock_repository(self):
        """Mock PTS repository"""
        repo = AsyncMock()
        repo.get_job.return_value = None
        repo.save_job.return_value = True
        return repo
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client"""
        redis_mock = Mock()
        redis_mock.setex = Mock()
        redis_mock.get = Mock(return_value=None)
        redis_mock.ping = Mock()
        return redis_mock
    
    @pytest.fixture
    def job_monitor(self, mock_repository, mock_redis):
        """Create job monitor with mocked dependencies"""
        monitor = PTSRenameJobMonitor(mock_repository, mock_redis)
        return monitor
    
    @pytest.mark.asyncio
    async def test_start_job_tracking(self, job_monitor):
        """Test starting job tracking"""
        job_id = "test_job_123"
        task_id = "test_task_456"
        job_config = {
            'total_files': 5,
            'upload_id': 'upload_789'
        }
        
        result = await job_monitor.start_job_tracking(job_id, task_id, job_config)
        
        assert result is True
        # Verify Redis calls were made
        job_monitor.redis_client.setex.assert_called()
    
    @pytest.mark.asyncio
    async def test_update_job_progress(self, job_monitor):
        """Test job progress updates"""
        job_id = "test_job_123"
        task_id = "test_task_456"
        
        result = await job_monitor.update_job_progress(
            job_id, task_id, PTSJobPhase.PROCESSING_FILES, 50,
            "Processing file 3 of 6"
        )
        
        assert result is True
        # Verify Redis storage was called
        job_monitor.redis_client.setex.assert_called()
    
    @pytest.mark.asyncio
    async def test_complete_job_tracking(self, job_monitor):
        """Test job completion tracking"""
        job_id = "test_job_123"
        task_id = "test_task_456"
        result_data = {
            'processed_files': 5,
            'processing_time': 45.2,
            'download_url': 'http://example.com/download/123'
        }
        
        success = await job_monitor.complete_job_tracking(job_id, task_id, result_data)
        
        assert success is True
        # Verify completion status was stored
        job_monitor.redis_client.setex.assert_called()
    
    @pytest.mark.asyncio
    async def test_fail_job_tracking(self, job_monitor):
        """Test job failure tracking"""
        job_id = "test_job_123"
        task_id = "test_task_456"
        error = "File processing failed"
        
        success = await job_monitor.fail_job_tracking(
            job_id, task_id, error, "ProcessingError", retryable=True
        )
        
        assert success is True
        # Verify failure was recorded
        job_monitor.redis_client.setex.assert_called()


class TestPTSRenameTaskService:
    """Test PTS task service functionality"""
    
    @pytest.fixture
    def mock_repository(self):
        """Mock repository with test data"""
        repo = AsyncMock()
        
        # Mock PTS files
        pts_files = [
            PTSFile(
                file_id="file_1",
                filename="test1.pts",
                original_path="/tmp/test1.pts",
                size=1024,
                checksum="abc123"
            ),
            PTSFile(
                file_id="file_2", 
                filename="test2.pts",
                original_path="/tmp/test2.pts",
                size=2048,
                checksum="def456"
            )
        ]
        
        repo.get_pts_files.return_value = pts_files
        repo.get_job.return_value = None
        repo.save_job.return_value = True
        
        return repo
    
    @pytest.fixture
    def mock_monitor(self):
        """Mock job monitor"""
        monitor = AsyncMock()
        monitor.start_job_tracking.return_value = True
        monitor.get_job_status.return_value = {
            'job_id': 'test_job',
            'status': 'completed',
            'progress': 100
        }
        return monitor
    
    @pytest.fixture
    def task_service(self, mock_repository, mock_monitor):
        """Create task service with mocked dependencies"""
        return PTSRenameTaskService(mock_repository, mock_monitor)
    
    @pytest.mark.asyncio
    async def test_submit_pts_rename_job_validation_error(self, task_service):
        """Test job submission with validation error"""
        # Empty request should fail validation
        job_request = PTSRenameJobRequest(
            upload_id="",  # Empty upload_id should fail
            operations=[PTSOperationType.RENAME]
        )
        
        with pytest.raises(ValueError, match="Upload ID is required"):
            await task_service.submit_pts_rename_job(job_request)
    
    @pytest.mark.asyncio
    async def test_submit_pts_rename_job_no_files(self, task_service):
        """Test job submission with no PTS files"""
        # Mock repository to return no files
        task_service.repository.get_pts_files.return_value = []
        
        job_request = PTSRenameJobRequest(
            upload_id="upload_123",
            operations=[PTSOperationType.RENAME]
        )
        
        with pytest.raises(ValueError, match="No PTS files found"):
            await task_service.submit_pts_rename_job(job_request)
    
    @pytest.mark.asyncio
    @patch('backend.pts_renamer.services.pts_rename_task_service.is_dramatiq_available')
    @patch('backend.pts_renamer.services.pts_rename_task_service.get_dramatiq_task')
    async def test_submit_pts_rename_job_success(self, mock_get_task, mock_available, task_service):
        """Test successful job submission"""
        # Mock Dramatiq availability
        mock_available.return_value = True
        
        # Mock task
        mock_task = Mock()
        mock_message = Mock()
        mock_message.message_id = "task_123"
        mock_task.send.return_value = mock_message
        mock_get_task.return_value = mock_task
        
        job_request = PTSRenameJobRequest(
            upload_id="upload_123",
            operations=[PTSOperationType.RENAME, PTSOperationType.QC_GENERATION]
        )
        
        result = await task_service.submit_pts_rename_job(job_request)
        
        assert result['status'] == 'submitted'
        assert 'job_id' in result
        assert result['task_id'] == "task_123"
        
        # Verify task was submitted
        mock_task.send.assert_called_once()
        
        # Verify monitoring was started
        task_service.monitor.start_job_tracking.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_job_status(self, task_service):
        """Test getting job status"""
        job_id = "test_job_123"
        
        status = await task_service.get_job_status(job_id)
        
        assert status is not None
        assert status.job_id == job_id
        # Mock returns completed status
        assert status.status == PTSProcessingStatus.COMPLETED
        assert status.progress == 100


class TestDramatiqTasks:
    """Test Dramatiq task functions"""
    
    @pytest.fixture
    def mock_task_message(self):
        """Mock Dramatiq task message"""
        message = Mock()
        message.message_id = "test_task_123"
        return message
    
    @pytest.mark.asyncio
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._initialize_pts_services')
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._initialize_job_tracking')
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._update_job_progress')
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._cleanup_pts_task_completion')
    @patch('dramatiq.get_current_message')
    async def test_process_pts_rename_job_task(self, mock_get_message, mock_cleanup, 
                                             mock_progress, mock_tracking, mock_services):
        """Test PTS rename job task execution"""
        # Setup mocks
        mock_get_message.return_value = Mock(message_id="task_123")
        
        # Mock services
        mock_job = Mock()
        mock_job.pts_files = [
            Mock(filename="test1.pts", file_id="file1"),
            Mock(filename="test2.pts", file_id="file2")
        ]
        mock_job.start_processing = Mock()
        mock_job.mark_as_completed = Mock()
        mock_job.operations = [PTSOperationType.RENAME]
        
        mock_repo = AsyncMock()
        mock_repo.get_job.return_value = mock_job
        mock_repo.save_job.return_value = True
        
        mock_download_service = AsyncMock()
        mock_download_service.create_download_package.return_value = "http://example.com/download"
        
        services = {
            'repository': mock_repo,
            'processor': Mock(),
            'qc_generator': Mock(), 
            'directory_manager': Mock(),
            'download_service': mock_download_service
        }
        mock_services.return_value = services
        
        # Mock job tracking
        mock_tracking.return_value = None
        mock_progress.return_value = None
        mock_cleanup.return_value = None
        
        # Execute task
        job_data = {
            'job_id': 'job_123',
            'operations': ['rename'],
            'total_files': 2
        }
        
        with patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._process_single_pts_file') as mock_process:
            mock_process.return_value = {
                'file_id': 'file1',
                'filename': 'test1.pts',
                'status': 'success',
                'output_path': '/tmp/output1'
            }
            
            result = await process_pts_rename_job_task("job_123", job_data)
        
        # Verify result
        assert result['status'] == 'completed'
        assert result['job_id'] == 'job_123'
        assert result['success'] is True
        
        # Verify services were called
        mock_repo.get_job.assert_called_once_with("job_123")
        mock_repo.save_job.assert_called()
        mock_download_service.create_download_package.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration.create_download_archive_task')
    async def test_pts_file_compression_task(self, mock_archive_task):
        """Test PTS compression task"""
        mock_archive_result = {
            'status': 'completed',
            'archive_path': '/tmp/compressed.zip'
        }
        mock_archive_task.send.return_value = mock_archive_result
        
        result = await pts_file_compression_task("/tmp/source", "test_archive", "job_123")
        
        assert result['task_type'] == 'pts_file_compression'
        assert result['status'] == 'completed'
        assert result['job_id'] == 'job_123'
    
    @pytest.mark.asyncio
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._initialize_pts_services')
    async def test_pts_cleanup_task(self, mock_services):
        """Test PTS cleanup task"""
        # Mock services
        mock_repo = AsyncMock()
        mock_repo.delete_expired_jobs.return_value = 3
        
        services = {
            'repository': mock_repo
        }
        mock_services.return_value = services
        
        # Create temporary test files
        test_paths = ["/tmp/test1.txt", "/tmp/test2.txt"]
        
        with patch('pathlib.Path.exists', return_value=True), \
             patch('pathlib.Path.is_file', return_value=True), \
             patch('pathlib.Path.unlink') as mock_unlink:
            
            result = await pts_cleanup_task("job_123", test_paths)
        
        assert result['status'] == 'completed'
        assert result['job_id'] == 'job_123'
        assert result['expired_jobs_cleaned'] == 3


class TestMonitoringIntegration:
    """Test monitoring integration functionality"""
    
    @pytest.fixture
    def mock_repository(self):
        """Mock repository for monitoring tests"""
        repo = AsyncMock()
        repo.get_job_history.return_value = []
        return repo
    
    @pytest.fixture
    def mock_monitor(self):
        """Mock job monitor for monitoring tests"""
        monitor = AsyncMock()
        monitor.get_active_jobs.return_value = [
            {
                'job_id': 'job_1',
                'status': 'completed',
                'progress': 100,
                'created_at': datetime.now().isoformat(),
                'metrics': {
                    'processed_files': 5,
                    'total_files': 5
                }
            },
            {
                'job_id': 'job_2', 
                'status': 'processing_files',
                'progress': 60,
                'created_at': datetime.now().isoformat(),
                'metrics': {
                    'processed_files': 3,
                    'total_files': 5
                }
            }
        ]
        return monitor
    
    @pytest.fixture
    def monitoring_collector(self, mock_repository, mock_monitor):
        """Create monitoring collector with mocked dependencies"""
        return PTSMonitoringCollector(mock_repository, mock_monitor)
    
    @pytest.mark.asyncio
    async def test_collect_metrics(self, monitoring_collector):
        """Test metrics collection"""
        metrics = await monitoring_collector.collect_metrics()
        
        assert metrics['service_name'] == 'PTS File Renamer'
        assert 'job_statistics' in metrics
        assert 'system_health' in metrics
        assert 'performance' in metrics
        assert 'active_jobs_summary' in metrics
        
        # Check job statistics
        job_stats = metrics['job_statistics']
        assert job_stats['active_jobs'] == 2
        assert job_stats['completed_jobs'] == 1
    
    @pytest.mark.asyncio
    async def test_get_dashboard_data(self, monitoring_collector):
        """Test dashboard data formatting"""
        dashboard_data = await monitoring_collector.get_dashboard_data()
        
        assert dashboard_data['service_id'] == 'pts_renamer'
        assert dashboard_data['service_name'] == 'PTS File Renamer'
        assert 'kpis' in dashboard_data
        assert 'health_indicators' in dashboard_data
        assert 'recent_activity' in dashboard_data
        assert 'alerts' in dashboard_data
        
        # Check KPIs
        kpis = dashboard_data['kpis']
        assert len(kpis) == 4  # Active Jobs, Success Rate, Avg Processing Time, Files Processed Today
        
        # Check health indicators
        health_indicators = dashboard_data['health_indicators']
        assert len(health_indicators) == 3  # Task Queue, Database, Redis
    
    @pytest.mark.asyncio
    async def test_perform_health_check(self, monitoring_collector):
        """Test health check functionality"""
        with patch('backend.tasks.dramatiq_integration.is_dramatiq_available', return_value=True):
            health_status = await monitoring_collector.perform_health_check()
        
        assert health_status['service'] == 'pts_renamer'
        assert 'overall_status' in health_status
        assert 'checks' in health_status
        
        # Check individual health checks
        checks = health_status['checks']
        check_names = [check['check'] for check in checks]
        
        expected_checks = [
            'repository_connectivity',
            'dramatiq_availability', 
            'redis_connectivity',
            'stuck_jobs_detection'
        ]
        
        for expected_check in expected_checks:
            assert expected_check in check_names


@pytest.mark.integration
class TestPTSIntegrationFlow:
    """Integration tests for complete PTS processing flow"""
    
    @pytest.mark.asyncio
    @patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._initialize_pts_services')
    @patch('backend.tasks.dramatiq_integration.is_dramatiq_available')
    @patch('backend.tasks.dramatiq_integration.get_dramatiq_task')
    async def test_complete_pts_processing_flow(self, mock_get_task, mock_available, mock_services):
        """Test complete PTS processing flow from submission to completion"""
        # Setup mocks
        mock_available.return_value = True
        
        # Mock task submission
        mock_task = Mock()
        mock_message = Mock()
        mock_message.message_id = "integration_task_123"
        mock_task.send.return_value = mock_message
        mock_get_task.return_value = mock_task
        
        # Mock services
        mock_repo = AsyncMock()
        mock_pts_files = [
            PTSFile(
                file_id="file_1",
                filename="integration_test.pts", 
                original_path="/tmp/integration_test.pts",
                size=1024,
                checksum="integration123"
            )
        ]
        mock_repo.get_pts_files.return_value = mock_pts_files
        mock_repo.save_job.return_value = True
        
        mock_job = Mock()
        mock_job.pts_files = mock_pts_files
        mock_job.operations = [PTSOperationType.RENAME]
        mock_job.start_processing = Mock()
        mock_job.mark_as_completed = Mock()
        mock_repo.get_job.return_value = mock_job
        
        services = {
            'repository': mock_repo,
            'processor': Mock(),
            'qc_generator': Mock(),
            'directory_manager': Mock(), 
            'download_service': AsyncMock()
        }
        services['download_service'].create_download_package.return_value = "http://example.com/download"
        mock_services.return_value = services
        
        # Create test components
        monitor = PTSRenameJobMonitor(mock_repo)
        task_service = PTSRenameTaskService(mock_repo, monitor)
        
        # Step 1: Submit job
        job_request = PTSRenameJobRequest(
            upload_id="integration_upload_123",
            operations=[PTSOperationType.RENAME, PTSOperationType.QC_GENERATION]
        )
        
        submission_result = await task_service.submit_pts_rename_job(job_request)
        
        assert submission_result['status'] == 'submitted'
        job_id = submission_result['job_id']
        task_id = submission_result['task_id']
        
        # Step 2: Process job (simulate task execution)
        job_data = {
            'job_id': job_id,
            'operations': ['rename', 'qc_generation'],
            'total_files': 1
        }
        
        with patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._initialize_job_tracking'), \
             patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._update_job_progress'), \
             patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._process_single_pts_file') as mock_process, \
             patch('backend.pts_renamer.services.pts_rename_dramatiq_integration._cleanup_pts_task_completion'), \
             patch('dramatiq.get_current_message', return_value=Mock(message_id=task_id)):
            
            mock_process.return_value = {
                'file_id': 'file_1',
                'filename': 'integration_test.pts',
                'status': 'success',
                'output_path': '/tmp/processed/integration_test.pts'
            }
            
            processing_result = await process_pts_rename_job_task(job_id, job_data)
        
        # Step 3: Verify processing result
        assert processing_result['status'] == 'completed'
        assert processing_result['job_id'] == job_id
        assert processing_result['success'] is True
        assert processing_result['processed_files'] == 1
        
        # Step 4: Check final job status
        final_status = await task_service.get_job_status(job_id)
        # Note: This would return None from mock, but in real implementation would show completion
        
        print(f"Integration test completed successfully:")
        print(f"  Job ID: {job_id}")
        print(f"  Task ID: {task_id}")
        print(f"  Processing result: {processing_result['status']}")


if __name__ == '__main__':
    # Run tests with verbose output
    pytest.main([__file__, '-v', '--tb=short'])