# 資料庫災難恢復運行手冊

## 📋 概覽

本手冊提供資料庫系統發生故障時的完整恢復程序，包含自動化和手動恢復步驟、RTO/RPO 指標以及應急聯繫資訊。

## 🎯 恢復目標

| 指標 | 目標值 | 說明 |
|------|--------|------|
| **RTO** (Recovery Time Objective) | 30 分鐘 | 系統完全恢復運行的最大時間 |
| **RPO** (Recovery Point Objective) | 1 小時 | 可接受的最大資料遺失時間 |
| **服務可用性** | 99.9% | 年度目標可用性 |

## 🚨 故障分類與響應

### Level 1: 輕微故障 (5分鐘內解決)
- 單個查詢緩慢
- 連接池暫時滿載
- 磁碟使用率警告 (80-85%)

### Level 2: 中等故障 (15分鐘內解決)
- 資料庫連接失敗
- 查詢超時頻繁
- 磁碟使用率高 (85-95%)

### Level 3: 嚴重故障 (30分鐘內解決)
- 資料庫完全無法連接
- 資料檔案損毀
- 磁碟空間耗盡 (>95%)
- 主要功能完全失效

## 🔧 自動化恢復程序

### 1. 監控系統自動檢測

系統每 5 分鐘執行健康檢查，自動檢測以下問題：

```bash
# 自動健康檢查
python scripts/database_health_monitor.py --once

# 持續監控模式
python scripts/database_health_monitor.py --monitor
```

### 2. 自動故障切換

當檢測到 Level 2+ 故障時，系統自動執行：

1. **連接池重置**
   ```python
   # 重置連接池
   from scripts.database_maintenance import DatabaseConnectionPool
   pool.close_all()
   pool.reinitialize()
   ```

2. **讀取副本切換**
   ```bash
   # 切換到讀取副本
   python scripts/database_maintenance.py --sync-replicas
   ```

3. **自動備份觸發**
   ```bash
   # 緊急備份
   python scripts/database_backup_automation.py --type emergency
   ```

### 3. 自動告警通知

- 📧 郵件告警 (管理員群組)
- 💬 LINE 通知 (運維人員)
- 📊 監控儀表板顯示

## 🛠 手動恢復程序

### Level 1 故障處理

#### 查詢性能問題
```bash
# 1. 檢查當前查詢
python scripts/database_health_monitor.py --report

# 2. 分析慢查詢
sqlite3 data/email_inbox.db ".explain query plan SELECT ..."

# 3. 重建索引（如需要）
python scripts/database_maintenance.py --reindex
```

#### 連接池問題
```bash
# 1. 檢查連接池狀態
python scripts/database_maintenance.py --pool-stats

# 2. 重置連接池
# 在應用中重新初始化連接池
```

### Level 2 故障處理

#### 資料庫連接失敗
```bash
# 1. 檢查資料庫檔案完整性
python scripts/database_health_monitor.py --once

# 2. 嘗試修復
sqlite3 data/email_inbox.db "PRAGMA integrity_check;"

# 3. 如果損壞，從備份恢復
python scripts/restore_from_backup.py --latest-daily
```

#### 磁碟空間不足
```bash
# 1. 清理舊資料
python scripts/database_maintenance.py --cleanup 30

# 2. 執行 VACUUM 優化
python scripts/database_maintenance.py --vacuum

# 3. 清理日誌檔案
find logs/ -name "*.log" -mtime +7 -delete
```

### Level 3 故障處理

#### 資料庫完全損壞

**步驟 1: 立即停止服務**
```bash
# 停止所有相關服務
pkill -f "python frontend/app.py"
pkill -f "dramatiq"
```

**步驟 2: 評估損壞程度**
```bash
# 檢查檔案完整性
file data/email_inbox.db
ls -la data/email_inbox.db

# 嘗試 SQLite 修復
sqlite3 data/email_inbox.db ".recover recovery.db"
```

**步驟 3: 從備份恢復**
```bash
# 1. 移動損壞的資料庫
mv data/email_inbox.db data/email_inbox.db.corrupted.$(date +%Y%m%d_%H%M%S)

# 2. 找到最新的有效備份
python scripts/database_backup_automation.py --status

# 3. 恢復備份
# 從每日備份恢復
cp backups/automated/daily/email_inbox_backup_daily_YYYYMMDD_HHMMSS.db data/email_inbox.db

# 或從週週備份恢復（如果更新）
gunzip -c backups/automated/weekly/email_inbox_backup_weekly_YYYYMMDD_HHMMSS.db.gz > data/email_inbox.db
```

**步驟 4: 驗證恢復**
```bash
# 驗證資料庫完整性
python scripts/database_health_monitor.py --once

# 檢查關鍵表資料
sqlite3 data/email_inbox.db "SELECT COUNT(*) FROM emails;"
sqlite3 data/email_inbox.db "SELECT COUNT(*) FROM email_download_status;"
```

**步驟 5: 重啟服務**
```bash
# 重新啟動服務
python start_integrated_services.py
```

#### 磁碟空間完全耗盡

**緊急清理步驟:**
```bash
# 1. 清理最大的日誌檔案
du -h logs/* | sort -hr | head -10
rm logs/application.log.*

# 2. 清理臨時檔案
rm -rf temp/uploads/*
rm -rf __pycache__/

# 3. 清理舊備份
find backups/automated/daily -name "*.db" -mtime +7 -delete

# 4. 執行緊急資料清理
python scripts/database_maintenance.py --cleanup 7
```

## 📊 監控指標與閾值

### 關鍵指標監控

| 指標 | 正常範圍 | 警告閾值 | 嚴重閾值 |
|------|----------|----------|----------|
| 資料庫大小 | < 500MB | 500-1000MB | > 1000MB |
| 連接時間 | < 100ms | 100-500ms | > 500ms |
| 查詢執行時間 | < 1000ms | 1000-3000ms | > 3000ms |
| 磁碟使用率 | < 80% | 80-90% | > 90% |
| 活躍連接數 | < 50% 池大小 | 50-80% | > 80% |
| 備份間隔 | < 12小時 | 12-24小時 | > 24小時 |

### 自動告警配置

```json
{
  "thresholds": {
    "max_db_size_mb": 1000,
    "max_connection_time_ms": 500,
    "max_query_time_ms": 3000,
    "max_disk_usage_percent": 90,
    "min_backup_age_hours": 24
  },
  "alert_settings": {
    "email_enabled": true,
    "email_recipients": [
      "<EMAIL>",
      "<EMAIL>"
    ],
    "line_notification": true,
    "escalation_delay_minutes": 15
  }
}
```

## 🔄 備份策略

### 自動備份排程

```bash
# 每日備份 (00:30)
30 0 * * * /usr/bin/python3 /path/to/scripts/database_backup_automation.py --type daily

# 每週備份 (Sunday 02:00)
0 2 * * 0 /usr/bin/python3 /path/to/scripts/database_backup_automation.py --type weekly

# 每月備份 (1st 03:00)
0 3 1 * * /usr/bin/python3 /path/to/scripts/database_backup_automation.py --type monthly
```

### 備份保留策略

- **每日備份**: 保留 7 天
- **每週備份**: 保留 4 週
- **每月備份**: 保留 12 個月

### 備份驗證

```bash
# 每日自動驗證最新備份
python scripts/verify_backup_integrity.py --latest-daily

# 手動驗證特定備份
python scripts/verify_backup_integrity.py --file backups/daily/backup_YYYYMMDD.db
```

## 📞 應急聯繫資訊

### 第一響應團隊
- **資料庫管理員**: <EMAIL> (手機: +886-XXX-XXXXXX)
- **系統管理員**: <EMAIL> (手機: +886-XXX-XXXXXX)
- **開發團隊負責人**: <EMAIL> (手機: +886-XXX-XXXXXX)

### 升級路徑
1. **Level 1**: 自動處理 → 通知運維團隊
2. **Level 2**: 運維團隊響應 (15分鐘內)
3. **Level 3**: 升級至資料庫管理員 + 開發負責人

### 外部支援
- **雲端服務商支援**: (如適用)
- **硬體廠商支援**: (如適用)

## 📝 故障記錄表單

### 事件記錄範本

```
事件ID: INC-YYYYMMDD-001
發生時間: YYYY-MM-DD HH:MM:SS
發現方式: [自動監控/人工發現/用戶回報]
故障等級: [Level 1/2/3]
影響範圍: [系統功能/用戶數量]

故障現象:
- 

根本原因:
- 

解決步驟:
1. 
2. 
3. 

恢復時間: YYYY-MM-DD HH:MM:SS
資料遺失: [是/否] [描述]
後續改進:
- 

負責人員: [姓名]
```

## 🧪 恢復測試計畫

### 定期測試排程

- **每月**: 備份恢復測試
- **每季**: 完整災難恢復演練
- **每年**: 業務連續性測試

### 測試檢查清單

#### 備份恢復測試
- [ ] 確認最新備份可以成功恢復
- [ ] 驗證恢復後資料完整性
- [ ] 測試應用程式功能正常
- [ ] 確認性能指標在正常範圍
- [ ] 記錄恢復時間

#### 災難恢復演練
- [ ] 模擬各種故障場景
- [ ] 測試告警系統響應
- [ ] 驗證故障切換程序
- [ ] 確認團隊響應時間
- [ ] 檢查文件是否更新

## 📈 持續改進

### 性能優化建議

1. **索引優化**
   - 定期分析查詢計劃
   - 根據使用模式調整索引

2. **連接池調整**
   - 監控連接使用模式
   - 動態調整池大小

3. **資料分割**
   - 考慮歷史資料歸檔
   - 實施資料生命週期管理

### 監控改進

1. **預測性告警**
   - 基於趨勢的告警
   - 機器學習異常檢測

2. **自動化程度**
   - 增加自動恢復場景
   - 減少人工介入需求

---

## ⚠️ 重要提醒

1. **永遠先備份** - 在執行任何恢復操作前，先備份當前狀態
2. **測試備份** - 未經測試的備份等於沒有備份
3. **文件更新** - 每次事件後更新此運行手冊
4. **團隊培訓** - 確保所有團隊成員熟悉恢復程序
5. **權限管理** - 確保應急人員有適當的系統存取權限

---

*本文件最後更新: 2025-08-19*  
*版本: 1.0*  
*負責人: Database Administrator*