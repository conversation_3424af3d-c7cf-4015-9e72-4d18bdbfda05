#!/usr/bin/env python3
"""
Summary 工作表生成器
基於實際 BIN 資料生成完整的 Summary 工作表

對照 VBA_TO_PYTHON_MAPPING.md ### Summary 工作表產生注意事項：
1. BIN 排序規則：先按 Count 降序，再按 BIN 號碼升序
2. 完整 BIN 列表顯示：即使 Count=0 也要列出所有可能的 BIN
3. 百分比計算：使用小數點格式 (0.6 = 60%)
4. Definition 對應：BIN 1="All Pass"，其他=測試項目名稱
5. 多 Site 統計：每個 Site 佔 2 欄 (Count + %)
"""

import pandas as pd
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import os


@dataclass
class BinStatistics:
    """BIN 統計資料結構"""
    count: int
    percentage: float


@dataclass  
class SiteStatistics:
    """Site 統計資料結構"""
    total: int
    pass_count: int
    fail: int
    pass_rate: float
    bins: Dict[int, int]


class SummaryGenerator:
    """
    Summary 工作表生成器
    接收 fixed_test.py 的 BIN 資料，生成完整 Summary 工作表
    """
    
    def __init__(self, enable_logging: bool = True):
        """初始化 Summary 生成器"""
        self.enable_logging = enable_logging
        self.max_pass_bin = 4  # VBA 設計：支援 4 個 Pass BIN (1,2,3,4)
        
        if self.enable_logging:
            print("初始化 Summary 工作表生成器")
    
    def calculate_bin_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        計算 BIN 統計
        對照 VBA_TO_PYTHON_MAPPING.md：BIN 排序規則
        """
        # 獲取所有設備的 BIN 分配結果
        # 真實資料：設備資料從第13行開始 (index 12)，第12行 (index 11) 是單位行
        device_rows = df.iloc[12:]  # 設備資料從第13行開始 (index 12)
        
        # BIN 欄位固定在第2欄 (index 1)，根據真實 CSV 結構
        bin_column = 1
        
        if self.enable_logging:
            print(f"[CHART] BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄")
        
        # 統計各 BIN 數量
        bin_counts = {}
        total_devices = 0
        
        for idx in device_rows.index:
            if bin_column < len(df.columns) and pd.notna(df.iloc[idx, bin_column]):
                try:
                    bin_value = df.iloc[idx, bin_column]
                    # 跳過標頭行或非數字內容
                    if str(bin_value).lower() in ['bin#', 'bin', '']:
                        continue
                    
                    bin_num = int(bin_value)
                    bin_counts[bin_num] = bin_counts.get(bin_num, 0) + 1
                    total_devices += 1
                except (ValueError, TypeError) as e:
                    if self.enable_logging:
                        print(f"[WARNING] 跳過無效 BIN 號碼: {df.iloc[idx, bin_column]} (行 {idx+1})")
                    continue
        
        if self.enable_logging:
            print(f"[CHART] BIN 統計: 總設備 {total_devices} 個，BIN 分佈 {bin_counts}")
        
        # 計算 Pass/Fail 統計 (使用新的多 Pass BIN 方法)
        pass_devices = self.calculate_pass_devices(bin_counts, self.max_pass_bin)
        fail_devices = total_devices - pass_devices
        
        # 計算百分比並按特殊排序：BIN 1 永遠在第一行，其他按 Count 降序排列
        bin_distribution = {}
        
        # 自定義排序函數：BIN 1 優先，其他按 Count 降序, BIN 號碼升序
        def custom_bin_sort(item):
            bin_num, count = item
            if bin_num == 1:
                return (0, -count, bin_num)  # BIN 1 永遠排第一 (priority 0)
            else:
                return (1, -count, bin_num)  # 其他 BIN 按 Count 降序, BIN 升序 (priority 1)
        
        for bin_num, count in sorted(bin_counts.items(), key=custom_bin_sort):
            bin_distribution[bin_num] = {
                'count': count,
                'percentage': round(count / total_devices * 100, 3) if total_devices > 0 else 0.0
            }
        
        return {
            'total_devices': total_devices,
            'pass_devices': pass_devices,
            'fail_devices': fail_devices,
            'yield_rate': pass_devices / total_devices * 100 if total_devices > 0 else 0.0,
            'bin_distribution': bin_distribution
        }
    
    def calculate_pass_devices(self, bin_distribution: Dict[int, int], max_pass_bin: int = 4) -> int:
        """
        計算 Pass 設備數 (包含所有 Pass BIN)
        對照 VBA_TO_PYTHON_MAPPING.md: BIN1 + BIN2 + BIN3 + BIN4
        
        Args:
            bin_distribution: BIN 分佈字典 {bin_num: count}
            max_pass_bin: 最大 Pass BIN 號碼 (預設 4)
            
        Returns:
            Pass 設備總數
        """
        pass_devices = 0
        for bin_num in range(1, max_pass_bin + 1):
            pass_devices += bin_distribution.get(bin_num, 0)
        return pass_devices
    
    def calculate_site_statistics(self, df: pd.DataFrame, site_column: int) -> Dict[int, Dict[str, Any]]:
        """
        計算 Site 統計
        對照 VBA_TO_PYTHON_MAPPING.md：各 Site 分別統計 BIN 分布
        """
        # 真實資料：設備資料從第13行開始 (index 12)
        device_rows = df.iloc[12:]  # 設備資料從第13行開始
        
        # BIN 欄位固定在第2欄 (index 1)
        bin_column = 1
        
        if self.enable_logging:
            if site_column is not None and site_column >= 0:
                print(f"[CHART] Site 統計計算: 設備資料從第13行開始，Site 欄位在第{site_column+1}欄，BIN 欄位在第2欄")
            else:
                print(f"[CHART] Site 統計計算: 設備資料從第13行開始，未找到 Site 欄位，BIN 欄位在第2欄")
        
        site_stats = {}
        
        # 當沒有 Site 欄位時，返回空的統計
        if site_column is None or site_column < 0:
            if self.enable_logging:
                print(f"[CHART] Site 統計: 0 個 Site，詳細資料 {{}}")
            return {}
        
        for idx in device_rows.index:
            if (site_column < len(df.columns) and bin_column < len(df.columns) and 
                pd.notna(df.iloc[idx, site_column]) and pd.notna(df.iloc[idx, bin_column])):
                
                try:
                    site_value = df.iloc[idx, site_column]
                    bin_value = df.iloc[idx, bin_column]
                    
                    # 跳過標頭行或非數字內容
                    if (str(site_value).lower() in ['site_no', 'site', ''] or 
                        str(bin_value).lower() in ['bin#', 'bin', '']):
                        continue
                    
                    # 修復浮點格式字串轉換問題 (如 "1.0000    ")
                    site_no = int(float(str(site_value).strip()))
                    bin_no = int(bin_value)
                    
                    # 初始化 Site 統計
                    if site_no not in site_stats:
                        site_stats[site_no] = {
                            'total': 0,
                            'pass': 0, 
                            'fail': 0,
                            'pass_rate': 0.0,
                            'bins': {}
                        }
                    
                    # 累計統計
                    site_stats[site_no]['total'] += 1
                    site_stats[site_no]['bins'][bin_no] = site_stats[site_no]['bins'].get(bin_no, 0) + 1
                    
                    # 判斷 Pass/Fail
                    if bin_no <= self.max_pass_bin:
                        site_stats[site_no]['pass'] += 1
                    else:
                        site_stats[site_no]['fail'] += 1
                        
                except (ValueError, TypeError) as e:
                    if self.enable_logging:
                        site_val = df.iloc[idx, site_column] if site_column < len(df.columns) else "N/A"
                        bin_val = df.iloc[idx, bin_column] if bin_column < len(df.columns) else "N/A"
                        print(f"[WARNING] 跳過無效資料: Site='{site_val}', BIN='{bin_val}' (行 {idx+1}) - {str(e)}")
                    continue
        
        # 計算各 Site 的 Pass 率
        for site_no in site_stats:
            total = site_stats[site_no]['total']
            if total > 0:
                site_stats[site_no]['pass_rate'] = site_stats[site_no]['pass'] / total * 100
        
        if self.enable_logging:
            print(f"[CHART] Site 統計: {len(site_stats)} 個 Site，詳細資料 {site_stats}")
        
        return site_stats
    
    def get_bin_definitions(self, df: pd.DataFrame) -> Dict[int, str]:
        """
        獲取 BIN Definition 映射
        對照 VBA_TO_PYTHON_MAPPING.md：BIN 1="All Pass"，其他=測試項目名稱
        
        實際從第8行 (index 7) 和第6行 (index 5) BIN 分配對應：
        - BIN 1: "All Pass" (固定)
        - 其他 BIN: 根據第6行 BIN 分配找到對應第8行測試項目名稱
        """
        definitions = {1: "All Pass"}  # BIN 1 固定定義
        
        if len(df) <= 7:
            return definitions
        
        # 從第8行 (index 7) 獲取測試項目名稱 - 修正：真實名稱在第8行
        test_item_row = df.iloc[7]
        
        # 從第6行 (index 5) 獲取 BIN 分配
        bin_assignment_row = df.iloc[5] if len(df) > 5 else None
        
        # 建立 BIN 號碼到測試項目名稱的對應
        for col_idx in range(len(df.columns)):
            # 跳過前幾欄的基本資訊欄位
            if col_idx < 5:  # 跳過 Serial#, Bin#, Time, Index_Time, Site_No
                continue
                
            # 獲取測試項目名稱
            test_name = test_item_row.iloc[col_idx] if col_idx < len(test_item_row) else ""
            
            # 獲取該欄位對應的 BIN 號碼
            if bin_assignment_row is not None and col_idx < len(bin_assignment_row):
                try:
                    bin_num = int(bin_assignment_row.iloc[col_idx])
                    
                    # 確保測試項目名稱有效
                    if (pd.notna(test_name) and 
                        str(test_name).strip() and 
                        str(test_name).lower() not in ['test_time', 'index_time', 'site_no', 'bin#', '', 'none']):
                        definitions[bin_num] = str(test_name).strip()
                        
                except (ValueError, TypeError):
                    continue
        
        if self.enable_logging:
            print(f"[BOARD] BIN Definition 對應: {len(definitions)} 個 BIN 定義")
        
        return definitions
    
    def calculate_percentage(self, count: int, total: int) -> float:
        """
        計算百分比 - 小數點格式
        對照 VBA_TO_PYTHON_MAPPING.md：0.6 = 60%
        """
        if total == 0:
            return 0.0
        return round(count / total * 100, 3)
    
    def generate_summary_headers(self, df: pd.DataFrame, site_column: int) -> List[str]:
        """
        生成動態 Summary 標頭
        對照 VBA_TO_PYTHON_MAPPING.md：每個 Site 佔 2 欄
        """
        # 基本欄位 (固定 5 欄)
        headers = ['Bin', 'Count', '%', 'Definition', 'Note']
        
        # 獲取有資料的 Site，真實資料從第13行開始
        device_rows = df.iloc[12:]
        active_sites = set()
        
        # 當沒有 Site 欄位時，直接返回基本標頭
        if site_column is None or site_column < 0:
            return headers
        
        for idx in device_rows.index:
            if (site_column < len(df.columns) and pd.notna(df.iloc[idx, site_column])):
                try:
                    site_value = df.iloc[idx, site_column]
                    # 跳過標頭行
                    if str(site_value).lower() in ['site_no', 'site', '']:
                        continue
                    # 修復浮點格式字串轉換問題 (如 "1.0000    ")
                    site_no = int(float(str(site_value).strip()))
                    active_sites.add(site_no)
                except (ValueError, TypeError) as e:
                    if self.enable_logging:
                        site_val = df.iloc[idx, site_column] if site_column < len(df.columns) else "N/A"
                        print(f"[WARNING] 跳過無效 Site 值: '{site_val}' (行 {idx+1}) - {str(e)}")
                    continue
        
        # 為每個 Site 添加 2 欄 (Count + %)
        for site_no in sorted(active_sites):
            headers.extend([f'Site {site_no}', '%'])
        
        return headers
    
    def generate_summary(self, df: pd.DataFrame, site_column: int, csv_file_path: str) -> Dict[str, Any]:
        """
        生成完整 Summary 工作表
        對照 doc/KDD0530D3.D_sample1.xlsx 範本結構
        """
        if self.enable_logging:
            print(f"[TARGET] 生成 Summary 工作表: {os.path.basename(csv_file_path)}")
        
        # 1. 計算 BIN 統計
        bin_stats = self.calculate_bin_statistics(df)
        
        # 2. 計算 Site 統計
        site_stats = self.calculate_site_statistics(df, site_column)
        
        # 3. 獲取 BIN 定義
        bin_definitions = self.get_bin_definitions(df)
        
        # 4. 生成動態標頭
        headers = self.generate_summary_headers(df, site_column)
        
        # 5. 生成基本統計行 (第1-4行)
        total_cols = len(headers)
        basic_stats = [
            ['Total', bin_stats['total_devices']] + [''] * (total_cols - 2),
            ['Pass', bin_stats['pass_devices']] + [''] * (total_cols - 2), 
            ['Fail', bin_stats['fail_devices']] + [''] * (total_cols - 2),
            ['Yield', bin_stats['yield_rate']] + [''] * (total_cols - 2)
        ]
        
        # 6. 生成 Site 總計行 (第5行)
        site_total_row = ['', '', '', '', '']  # 前5欄空白
        
        # 檢查 site_stats 是否有效
        active_sites = []
        if site_stats and isinstance(site_stats, dict):
            active_sites = sorted(site_stats.keys())
            for site_no in active_sites:
                site_total_row.extend(['Total', site_stats[site_no]['total']])
        
        # 補齊剩餘欄位
        while len(site_total_row) < total_cols:
            site_total_row.append('')
        
        # 7. 生成 BIN 資料行 (確保 BIN 1 永遠在第一行)
        bin_rows = []
        
        # 強制添加 BIN 1 作為第一行 (即使沒有設備)
        if 1 not in bin_stats['bin_distribution']:
            # 如果沒有 BIN 1，創建一個 Count=0 的 BIN 1 行
            row = [
                1,                                    # BIN 號碼
                0,                                    # Count (0 個設備)
                0.0,                                  # 總百分比 (0%)
                "All Pass",                           # Definition (固定)
                ''                                    # Note (空白)
            ]
            
            # 為每個 Site 添加 BIN 1 的數量和百分比 (都是 0)
            if active_sites and site_stats:
                for site_no in active_sites:
                    row.extend([0, 0.0])  # 0 個設備, 0%
            
            # 補齊剩餘欄位
            while len(row) < total_cols:
                row.append('')
            
            bin_rows.append(row)
        
        # 添加其他實際存在的 BIN 資料
        for bin_no, bin_data in bin_stats['bin_distribution'].items():
            row = [
                bin_no,                                    # BIN 號碼
                bin_data['count'],                         # Count
                bin_data['percentage'],                    # 總百分比
                bin_definitions.get(bin_no, f"Bin {bin_no}"),  # Definition
                ''                                         # Note (空白)
            ]
            
            # 為每個 Site 添加該 BIN 的數量和百分比
            if active_sites and site_stats:
                for site_no in active_sites:
                    site_bin_count = site_stats[site_no]['bins'].get(bin_no, 0)
                    site_total = site_stats[site_no]['total']
                    site_percentage = self.calculate_percentage(site_bin_count, site_total)
                    
                    row.extend([site_bin_count, site_percentage])
            
            # 補齊剩餘欄位
            while len(row) < total_cols:
                row.append('')
            
            bin_rows.append(row)
        
        if self.enable_logging:
            print(f"[OK] Summary 生成完成: {len(bin_rows)} 個 BIN, {len(active_sites)} 個 Site")
        
        return {
            'basic_stats': basic_stats,
            'headers': headers,
            'bin_rows': bin_rows,
            'site_total_row': site_total_row,
            'statistics': {
                'bin_stats': {
                    **bin_stats,
                    'bin_definitions': bin_definitions
                },
                'site_stats': site_stats
            }
        }