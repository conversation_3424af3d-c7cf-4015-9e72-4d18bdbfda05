# PTS Renamer Development Guidelines

## Overview

This document provides comprehensive development guidelines for the PTS (Parametric Test System) File Renamer integration project. It ensures proper implementation of MVP architecture using **unified database approach** while preparing for Vue.js + FastAPI migration and maintaining integration with existing Dramatiq infrastructure.

### Unified Database Architecture Summary

**Architecture Decision**: Use existing `outlook.db` database with `pts_rename_` table prefixes

**Key Benefits**:
- ✅ **Operational Simplification**: Single database reduces management complexity
- ✅ **Infrastructure Reuse**: Leverages existing monitoring, backup, and connection pooling
- ✅ **Development Efficiency**: Consistent database patterns across all modules
- ✅ **Maintenance Reduction**: Single point of database administration
- ✅ **Cost Optimization**: No additional database infrastructure needed

**Implementation Approach**:
- PTS tables use `pts_rename_` prefix for logical separation
- Maintains functional independence while sharing database infrastructure
- All existing database tooling (backup, monitoring, health checks) automatically covers PTS data

## Architecture Principles

### MVP Architecture Pattern
- **Model**: Data models and business entities (`pts_rename_models.py`, `pts_rename_entities.py`)
- **View**: User interface components (Current: Flask templates, Future: Vue.js components)
- **Presenter**: Business logic controllers (`pts_rename_presenter.py`, Future: FastAPI endpoints)

### File Naming Convention
**MANDATORY**: All PTS Renamer files MUST use `pts_rename` prefix:
- Backend services: `pts_rename_processor.py`, `pts_rename_qc_generator.py`
- Frontend components: `pts_rename_main.html`, `pts_rename_upload.js`
- API routes: `pts_rename_flask_routes.py`, `pts_rename_fastapi_routes.py`
- Test files: `test_pts_rename_processor.py`

### Integration Requirements

#### Existing System Integration
- **Backend Location**: `backend/pts_renamer/` (following modular architecture)
- **Frontend Location**: `frontend/pts_renamer/` (following Flask blueprint pattern)
- **Web Access**: `http://localhost:5000/pts-renamer/` (Flask route)
- **Shared Infrastructure**: Use `backend/shared/` components for common functionality

#### Dramatiq Task Integration
**CRITICAL**: Reuse existing Dramatiq infrastructure:
- **Decompression**: Use existing `decompression` tasks for uploaded archives
- **Compression**: Use existing `compression` tasks for result packaging
- **Batch Processing**: Use existing `batch_processing` tasks for multiple files
- **File Processing**: Use existing `file_processing` tasks for file operations
- **Monitoring**: Integrate with existing Dramatiq monitoring dashboard

## Core Functionality Implementation

### File Processing Logic (Based on Desktop Version)

#### Renaming Processor (`pts_rename_processor.py`)
```python
# Pattern matching support:
# - Regex patterns: r'old_pattern' -> 'new_pattern'
# - Placeholders: {old}, {ext}, {num}
# - Example: "test_{num}.pts" -> "device_{num}_renamed.pts"
```

#### QC File Generation (`pts_rename_qc_generator.py`)
**EXACT LOGIC from desktop version**:
1. Remove data between "Parameter," and "QA," lines
2. Modify `QCOnlySBinAlter=1,0`
3. Recalculate `ParamCnt` based on non-empty lines
4. Filter [Bin Definition] to keep only bins 1 and 31
5. Generate `_QC` suffix filename

#### Directory Management (`pts_rename_directory_manager.py`)
**EXACT LOGIC from desktop version**:
1. Check filename vs folder name conflicts
2. Copy entire folder contents to new directory
3. Remove other PTS files (keep only target file)
4. Remove INI files from copied directory
5. Handle file conflicts and permissions

### Web Interface Implementation

#### Upload Service (`pts_rename_upload_service.py`)
- Support ZIP, 7Z, RAR file uploads
- Drag-and-drop functionality
- File validation and security scanning
- Integration with existing file storage infrastructure

#### Download Service (`pts_rename_download_service.py`)
- Auto-compress processed results
- Generate secure download URLs
- Automatic cleanup after retention period
- Integration with existing compression tasks

### Database Deployment and Monitoring

#### Unified Database Management
**Database Path**: `outlook.db`

**Key Advantages**:
```yaml
Database Setup:
  - Use existing outlook.db database infrastructure
  - PTS tables created with pts_rename_ prefix for logical separation
  - Leverage existing connection pooling and concurrent access management
  - No additional database initialization required

Monitoring Simplification:
  - PTS data monitored through existing outlook.db monitoring
  - PTS job status integrated into existing database dashboard
  - Uses existing database health checks and alerts
  - No additional database-specific monitoring needed
  - Simplified maintenance and troubleshooting

Backup Strategy Simplification:
  - PTS data included in existing outlook.db backups automatically
  - No additional backup scripts or configuration needed
  - Unified retention policies and backup verification
  - Single restore procedure for all data

Performance Benefits:
  - Single database reduces I/O overhead
  - Unified connection pool management
  - Better transaction support across all tables
  - Simplified performance monitoring and optimization
```

#### Configuration Management
```python
# Simplified configuration using unified database
DATABASE_CONFIG = {
    "unified": {
        "path": "outlook.db",  # Single database for all components
        "table_prefix": "pts_rename_",  # Logical separation through naming
        "timeout": 30,
        "max_connections": 10,  # Shared connection pool
        "backup_enabled": True,  # Existing backup infrastructure
        "monitoring_enabled": True  # Existing monitoring infrastructure
    }
}

# Monitoring integration (simplified)
MONITORING_CONFIG = {
    "databases": ["outlook.db"],  # Single database to monitor
    "health_checks": {
        "unified_db": {
            "query": "SELECT COUNT(*) FROM pts_rename_jobs",
            "timeout": 5,
            "alert_threshold": 10  # seconds
        }
    }
}
```

#### Schema Integration with Unified Database
PTS tables are created in existing `outlook.db`:
```sql
-- 1. Verify unified database exists
SELECT 'outlook.db ready for PTS integration' as status;

-- 2. Create PTS tables with prefix for logical separation
-- (handled by schema initialization scripts)

-- 3. Verify PTS tables created successfully
SELECT name FROM sqlite_master 
WHERE type='table' AND name LIKE 'pts_rename_%';

-- 4. Confirm unified database structure
SELECT COUNT(*) as table_count FROM sqlite_master 
WHERE type='table';
```

## Development Standards

### Code Quality Requirements
- **Type Hints**: ALL functions must have complete type annotations
- **Error Handling**: Use existing error handling patterns from `backend/shared/`
- **Logging**: Use existing logging infrastructure for audit trails
- **Testing**: Minimum 90% coverage for business logic

### Security Requirements
- **File Validation**: Validate all uploaded files for type and content
- **Size Limits**: Enforce reasonable file size limits
- **Timeout Protection**: Implement processing timeouts
- **Authentication**: Use existing authentication mechanisms

### Performance Requirements
- **Processing Time**: < 30 seconds for typical PTS files
- **Concurrent Users**: Support multiple simultaneous users
- **Memory Usage**: Efficient memory management for large files
- **Cleanup**: Automatic cleanup of temporary files

## Migration Strategy

### Phase 1: MVP Implementation (Current)
- Flask-based web interface
- Complete backend functionality
- Integration with existing Dramatiq tasks
- All desktop features implemented

### Phase 2: API Preparation
- FastAPI endpoints alongside Flask routes
- OpenAPI documentation
- API testing and validation

### Phase 3: Vue.js Migration
- Vue.js components replace Flask templates
- API-only backend communication
- Modern SPA user experience

## Testing Strategy

### Unit Testing
- Test all processing logic independently
- Mock external dependencies (file system, Dramatiq)
- Validate all business rules and edge cases

### Integration Testing
- Test API endpoints with real file uploads
- Verify Dramatiq task integration
- Test complete processing workflows

### E2E Testing
- Use Playwright for web interface testing
- Test complete user workflows
- Validate download functionality

## Deployment Guidelines

### Environment Configuration
- Use existing configuration management
- Environment-specific settings for file paths
- Integration with existing service discovery

### Monitoring Integration
- Register with existing monitoring dashboard
- Health check endpoints
- Performance metrics collection

### Error Handling
- Integration with existing error reporting
- Structured error responses
- Proper error categorization

## Development Workflow

### Before Starting Development
1. Read existing desktop implementation (`reNameCTAF/rename_pts_files.py`)
2. Understand web UI design (`reNameCTAF/pts_renamer_web.html`)
3. Review existing Dramatiq task implementations
4. Set up development environment with existing infrastructure

### During Development
1. Follow MVP architecture strictly
2. Use `pts_rename` prefix for all files
3. Integrate with existing shared components
4. Write tests before implementation (TDD)
5. Validate against desktop version functionality

### Before Deployment
1. Complete test coverage validation
2. Performance testing with large files
3. Security scanning and validation
4. Integration testing with existing services

## File Organization

### Backend Structure
```
backend/pts_renamer/
├── models/          # pts_rename_models.py, pts_rename_entities.py
├── services/        # pts_rename_processor.py, pts_rename_qc_generator.py
├── repositories/    # pts_rename_repository.py (unified database)
├── api/            # pts_rename_fastapi_routes.py
└── __init__.py
```

### Database Structure (Unified)
```
data/
├── outlook.db       # Unified database (includes PTS tables with pts_rename_ prefix)
└── backups/
    └── outlook/     # Unified database backups (includes all PTS data)
```

### Database Tables in outlook.db
```
# Email processing tables (existing)
- emails
- email_attachments
- processing_jobs

# PTS Renamer tables (new, with prefix)
- pts_rename_jobs
- pts_rename_files
- pts_rename_results
```

### Frontend Structure
```
frontend/pts_renamer/
├── routes/         # pts_rename_flask_routes.py
├── templates/      # pts_rename_main.html
├── static/         # pts_rename_upload.js, pts_rename_processor.js
└── __init__.py
```

### Test Structure
```
tests/
├── unit/           # test_pts_rename_processor.py
├── integration/    # test_pts_rename_api_integration.py
└── e2e/           # test_pts_rename_web_interface.py
```

## Implementation Status (Updated 2025-01-20)

### ✅ Task 1: Module Structure and Core Interfaces - COMPLETED
- **Backend Structure**: Complete modular directory structure established
  - `backend/pts_renamer/models/` - Domain entities and Pydantic models
  - `backend/pts_renamer/services/` - Core service orchestrator
  - `backend/pts_renamer/repositories/` - Data access layer
  - `backend/pts_renamer/api/` - API endpoints (prepared)
- **Frontend Structure**: Flask blueprint architecture ready
  - `frontend/pts_renamer/routes/` - Flask routes
  - `frontend/pts_renamer/templates/` - HTML templates
  - `frontend/pts_renamer/static/` - Static assets
  - `frontend/pts_renamer/components/` - Reusable components
- **MVP Architecture**: Complete implementation
  - Domain entities: `PTSFile`, `PTSQCFile`, `PTSDirectory`, `PTSProcessingJob`
  - Pydantic models: API validation and serialization
  - Service layer: `PTSRenameService` with Dramatiq integration
- **Infrastructure Integration**: Fully integrated with existing shared components
  - Task queue integration (`ITaskQueue`)
  - Logging integration (`LoggerManager`)
  - Hexagonal architecture compliance

### ✅ Completed Tasks
- **Task 1**: Set up PTS Renamer module structure and core interfaces - **COMPLETED**
- **Task 2**: Implement core data models and entities - **COMPLETED**
  - **Task 2.1**: Create PTS rename data models with Pydantic validation - **COMPLETED**
  - **Task 2.2**: Implement PTS business entities and value objects - **COMPLETED**
  - **Task 2.3**: Create repository interfaces and database models - **COMPLETED**
- **Task 3**: Implement file processing core services - **COMPLETED** (2025-01-20)
  - **Task 3.1**: Create PTS file renaming processor - **COMPLETED**
  - **Task 3.2**: Implement QC file generation service - **COMPLETED**
  - **Task 3.3**: Create directory management service - **COMPLETED**
- **Task 4**: Implement upload and file handling services - **COMPLETED** (2025-08-20)
  - **Task 4.1**: Create compressed file upload service - **COMPLETED**
  - **Task 4.2**: Implement download and compression service - **COMPLETED**

### ✅ Task 3: Implement file processing core services - COMPLETED (2025-01-20)
- **Task 3.1**: Create PTS file renaming processor - **COMPLETED**
  - File: `backend/pts_renamer/services/pts_rename_processor.py`
  - Features: Pattern matching, placeholder substitution, batch processing, preview support
- **Task 3.2**: Implement QC file generation service - **COMPLETED**
  - File: `backend/pts_renamer/services/pts_rename_qc_generator.py`
  - Features: Complete QC logic, content modification, bin filtering, batch processing
- **Task 3.3**: Create directory management service - **COMPLETED**
  - File: `backend/pts_renamer/services/pts_rename_directory_manager.py`
  - Features: Directory creation, folder copying, file cleanup, conflict handling

### ✅ Task 4: Implement upload and file handling services - COMPLETED (2025-08-20)
- **Task 4.1**: Create compressed file upload service - **COMPLETED**
  - File: `backend/pts_renamer/services/pts_rename_upload_service.py`
  - Features: Multi-format support (ZIP, 7Z, RAR), security validation, Dramatiq integration, PTS file detection, automatic cleanup
- **Task 4.2**: Implement download and compression service - **COMPLETED**
  - File: `backend/pts_renamer/services/pts_rename_download_service.py`
  - Features: Auto-compression, secure download URLs, result packaging, file cleanup

### 🔄 Next Tasks
- Task 5.1: Create Dramatiq task integration service
- Task 5.2: Implement job status tracking and monitoring

## Success Criteria

### Functional Requirements
- 🔄 All desktop functionality replicated in web interface (Core Services Complete)
- ✅ Complete integration with existing Dramatiq infrastructure
- ✅ Secure file upload and processing (Task 4 Complete)
- ✅ Automatic result compression and download (Task 4 Complete)

### Technical Requirements
- ✅ MVP architecture implementation (Tasks 1-4 Complete)
- ✅ Core file processing services (Task 3 Complete)
- ✅ Upload and download services (Task 4 Complete)
- 🔄 Complete test coverage (>90%) (Unit tests implemented)
- 🔄 Performance requirements met (Planned)
- ✅ Security requirements satisfied (Task 4 Complete)

### Integration Requirements
- ✅ Seamless integration with existing monitoring
- ✅ Proper error handling and logging
- ✅ Configuration management integration
- ✅ Service discovery registration

## References

- **Spec Location**: `.kiro/specs/pts-file-renamer-integration/`
- **Desktop Implementation**: `reNameCTAF/rename_pts_files.py`
- **Web UI Design**: `reNameCTAF/pts_renamer_web.html`
- **Existing Architecture**: `.kiro/steering/structure.md`
- **Technology Stack**: `.kiro/steering/tech.md`

## Usage Instructions

This steering document is automatically included when working on PTS Renamer tasks. Reference these guidelines when:
- Implementing PTS processing logic
- Creating web interface components
- Integrating with Dramatiq tasks
- Writing tests and documentation
- Planning deployment and configuration

For specific implementation details, refer to the task list in `.kiro/specs/pts-file-renamer-integration/tasks.md`.

---

## Summary of Unified Database Changes

### What Changed
- **Database Architecture**: From dedicated `pts_rename.db` → Unified `outlook.db`
- **Table Strategy**: PTS tables use `pts_rename_` prefix for logical separation
- **Infrastructure**: Leverages existing database infrastructure completely

### Developer Impact
- **Repository Layer**: Update connection strings to use `outlook.db`
- **Configuration**: Simplified database configuration (no additional DB setup)
- **Testing**: Use existing database test infrastructure
- **Monitoring**: PTS metrics automatically included in existing dashboards
- **Deployment**: No additional database deployment steps needed

### Operational Impact
- **Backup**: PTS data automatically included in existing backup processes
- **Monitoring**: PTS database health covered by existing outlook.db monitoring
- **Maintenance**: Single database maintenance window covers all data
- **Scaling**: Unified database scaling strategy for all components
- **Security**: Single database security configuration and access control

### Migration Considerations
- **New Deployments**: PTS tables created during schema initialization
- **Existing Systems**: If any PTS data exists, it can be migrated in-place
- **No Service Downtime**: Schema changes can be applied using existing database migration patterns

**Result**: Significantly simplified operational overhead while maintaining all functional requirements and logical separation of PTS functionality.