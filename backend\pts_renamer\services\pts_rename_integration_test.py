"""
PTS Renamer Integration Test

This module provides integration testing for the PTS Renamer Task 6 implementation
to verify that the MVP Presenter Layer business logic works correctly.
"""

import asyncio
import tempfile
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

from loguru import logger

# Import Task 6 components
from .pts_rename_service_factory import (
    PTSRenameServiceFactory,
    create_pts_rename_presenter,
    initialize_pts_renamer_services
)
from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameOperation,
    PTSRenameConfig
)


async def test_task_6_integration():
    """
    Integration test for Task 6: MVP Presenter Layer
    
    Tests the complete flow from presenter to service to repository
    """
    logger.info("🧪 Starting Task 6 Integration Test")
    
    try:
        # Test 1: Service Factory Creation
        logger.info("📋 Test 1: Service Factory Creation")
        
        # Create temporary database for testing
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as db_file:
            db_path = db_file.name
        
        # Initialize configuration
        config = PTSRenameConfig(
            max_file_size_mb=10,
            processing_timeout_seconds=60,
            enable_preview=True
        )
        
        # Create factory
        factory = PTSRenameServiceFactory(config=config, database_path=db_path)
        logger.info("✅ Service factory created successfully")
        
        # Test 2: Service Creation
        logger.info("📋 Test 2: Service Creation")
        service = factory.create_service()
        presenter = factory.create_presenter()
        logger.info("✅ Services created successfully")
        
        # Test 3: Mock File Upload Handling
        logger.info("📋 Test 3: Mock File Upload Handling")
        
        # Create mock file upload data
        mock_files = [
            MockFile("test_file_1.pts", 1024, "application/octet-stream"),
            MockFile("test_file_2.pts", 2048, "application/octet-stream")
        ]
        
        # Test upload request handling
        upload_response = await presenter.handle_upload_request(mock_files)
        
        if upload_response.get("success"):
            logger.info(f"✅ Upload handled successfully: {upload_response.get('upload_id')}")
        else:
            logger.warning(f"⚠️ Upload handling returned error: {upload_response.get('error')}")
        
        # Test 4: Processing Request Validation
        logger.info("📋 Test 4: Processing Request Validation")
        
        # Create mock processing request
        job_request = PTSRenameJobRequest(
            upload_id="test_upload_123",
            operations=[PTSRenameOperation.RENAME],
            rename_config={
                "old_pattern": r"test_file_(\d+)",
                "new_pattern": "renamed_file_{num}"
            },
            qc_enabled=False,
            create_directories=False
        )
        
        # Test processing request handling
        processing_response = await presenter.handle_processing_request(job_request)
        
        if processing_response.get("success"):
            logger.info(f"✅ Processing request handled: {processing_response.get('job_id')}")
        else:
            logger.info(f"📝 Processing request validation: {processing_response.get('error', {}).get('message')}")
        
        # Test 5: Error Handling
        logger.info("📋 Test 5: Error Handling")
        
        # Test invalid request
        invalid_request = PTSRenameJobRequest(
            upload_id="",  # Invalid empty upload_id
            operations=[PTSRenameOperation.RENAME],
            rename_config=None,  # Missing rename config
            qc_enabled=False,
            create_directories=False
        )
        
        error_response = await presenter.handle_processing_request(invalid_request)
        
        if not error_response.get("success"):
            logger.info("✅ Error handling working correctly")
            logger.debug(f"Error details: {error_response.get('error', {}).get('message')}")
        else:
            logger.warning("⚠️ Expected error not returned")
        
        # Test 6: Service Statistics
        logger.info("📋 Test 6: Service Statistics and Cleanup")
        
        # Test task queue statistics
        task_queue = factory.get_task_queue()
        stats = task_queue.get_queue_statistics()
        logger.info(f"📊 Task Queue Stats: {stats}")
        
        # Test repository statistics
        repository = factory.get_repository()
        try:
            job_stats = await repository.get_job_statistics(7)
            logger.info(f"📊 Job Statistics: {job_stats}")
        except Exception as e:
            logger.debug(f"Repository stats not available: {e}")
        
        # Cleanup
        factory.cleanup()
        logger.info("🧹 Factory cleanup completed")
        
        logger.info("✅ Task 6 Integration Test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task 6 Integration Test FAILED: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return False


class MockFile:
    """Mock file object for testing"""
    
    def __init__(self, filename: str, size: int, content_type: str):
        self.filename = filename
        self.content_length = size
        self.content_type = content_type
    
    def read(self):
        return b"mock file content"


async def test_presenter_validation():
    """Test presenter input validation"""
    logger.info("🧪 Testing Presenter Validation")
    
    try:
        presenter = create_pts_rename_presenter()
        
        # Test empty files
        result = await presenter.handle_upload_request([])
        assert not result.get("success"), "Should reject empty file list"
        logger.info("✅ Empty files validation working")
        
        # Test too many files
        many_files = [MockFile(f"file_{i}.pts", 1024, "application/octet-stream") for i in range(15)]
        result = await presenter.handle_upload_request(many_files)
        assert not result.get("success"), "Should reject too many files"
        logger.info("✅ File count validation working")
        
        # Test large file
        large_file = MockFile("large.pts", 200 * 1024 * 1024, "application/octet-stream")
        result = await presenter.handle_upload_request([large_file])
        assert not result.get("success"), "Should reject oversized files"
        logger.info("✅ File size validation working")
        
        logger.info("✅ Presenter Validation Tests PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Presenter Validation Tests FAILED: {e}")
        return False


def run_task_6_tests():
    """Run all Task 6 integration tests"""
    logger.info("🚀 Running Task 6: MVP Presenter Layer Integration Tests")
    
    try:
        # Run main integration test
        result1 = asyncio.run(test_task_6_integration())
        
        # Run validation tests
        result2 = asyncio.run(test_presenter_validation())
        
        if result1 and result2:
            logger.info("🎉 ALL TASK 6 TESTS PASSED")
            return True
        else:
            logger.error("💥 SOME TASK 6 TESTS FAILED")
            return False
            
    except Exception as e:
        logger.error(f"💥 Task 6 Test Runner FAILED: {e}")
        return False


if __name__ == "__main__":
    # Run tests if executed directly
    success = run_task_6_tests()
    exit(0 if success else 1)