"""
PTS Rename Monitoring Integration

This module integrates PTS rename operations with the existing monitoring
dashboard, providing real-time visibility into PTS processing jobs and
system health metrics.

Features:
- Real-time job monitoring for dashboard display
- Performance metrics collection and reporting
- Integration with existing monitoring infrastructure
- Health check endpoints for system status
- Alert generation for failure conditions
"""

import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional
from dataclasses import asdict

import redis
from loguru import logger

# Import PTS services
from .pts_rename_job_monitor import (
    PTSRenameJobMonitor, 
    PTSJobPhase, 
    PTSJobMetrics,
    get_pts_monitoring_data
)
from .pts_rename_task_service import PTSRenameTaskService
from ..repositories.pts_rename_repository import IPTSRenameRepository

# Import monitoring infrastructure
try:
    from backend.monitoring.collectors.dashboard_monitoring_coordinator import DashboardMonitoringCoordinator
    from backend.monitoring.models.monitoring_models import ServiceHealth, AlertLevel
    MONITORING_AVAILABLE = True
except ImportError:
    logger.warning("Dashboard monitoring not available for PTS integration")
    MONITORING_AVAILABLE = False


class PTSMonitoringCollector:
    """
    PTS monitoring data collector for dashboard integration
    
    This collector gathers PTS-specific metrics and integrates them
    with the existing monitoring dashboard infrastructure.
    """
    
    def __init__(self, 
                 repository: IPTSRenameRepository,
                 monitor: PTSRenameJobMonitor,
                 redis_client: Optional[redis.Redis] = None):
        """
        Initialize PTS monitoring collector
        
        Args:
            repository: PTS repository for data access
            monitor: Job monitoring service
            redis_client: Redis client for metrics storage
        """
        self.repository = repository
        self.monitor = monitor
        self.redis_client = redis_client or self._get_redis_client()
        
        # Metrics configuration
        self.metrics_key_prefix = "pts_metrics"
        self.health_check_interval = 30  # seconds
        self.metrics_retention = 3600    # 1 hour
        
    def _get_redis_client(self) -> Optional[redis.Redis]:
        """Get Redis client with error handling"""
        try:
            return redis.Redis(host="localhost", port=6379, db=0, decode_responses=True)
        except Exception as e:
            logger.warning(f"Failed to connect to Redis for PTS monitoring: {e}")
            return None
    
    async def collect_metrics(self) -> Dict[str, Any]:
        """
        Collect comprehensive PTS metrics for monitoring dashboard
        
        Returns:
            Dictionary containing all PTS metrics and status information
        """
        try:
            logger.debug("[PTS-MONITOR] Collecting PTS metrics")
            
            # Get active jobs
            active_jobs = await self.monitor.get_active_jobs()
            
            # Calculate job statistics
            job_stats = self._calculate_job_statistics(active_jobs)
            
            # Get system health
            system_health = await self._get_system_health()
            
            # Get performance metrics
            performance_metrics = await self._get_performance_metrics()
            
            # Get error statistics
            error_stats = await self._get_error_statistics()
            
            # Compile complete metrics
            metrics = {
                'service_name': 'PTS File Renamer',
                'status': system_health.get('status', 'unknown'),
                'timestamp': datetime.now().isoformat(),
                
                # Job statistics
                'job_statistics': job_stats,
                
                # System health
                'system_health': system_health,
                
                # Performance metrics
                'performance': performance_metrics,
                
                # Error statistics
                'errors': error_stats,
                
                # Active jobs summary
                'active_jobs_summary': {
                    'total': len(active_jobs),
                    'by_status': self._group_jobs_by_status(active_jobs),
                    'recent_jobs': active_jobs[:5]  # Last 5 jobs for dashboard
                }
            }
            
            # Store metrics in Redis for dashboard access
            await self._store_metrics(metrics)
            
            logger.debug(f"[PTS-MONITOR] Collected {len(active_jobs)} active jobs")
            return metrics
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to collect metrics: {e}")
            return self._get_error_metrics(str(e))
    
    async def get_dashboard_data(self) -> Dict[str, Any]:
        """
        Get data formatted for dashboard display
        
        Returns:
            Dashboard-formatted PTS data
        """
        try:
            metrics = await self.collect_metrics()
            
            # Format for dashboard consumption
            dashboard_data = {
                'service_id': 'pts_renamer',
                'service_name': 'PTS File Renamer',
                'status': metrics.get('status', 'unknown'),
                'last_updated': metrics['timestamp'],
                
                # Key performance indicators
                'kpis': [
                    {
                        'name': 'Active Jobs',
                        'value': metrics['job_statistics']['active_jobs'],
                        'trend': 'stable',  # Could be calculated from historical data
                        'color': 'blue'
                    },
                    {
                        'name': 'Success Rate',
                        'value': f"{metrics['job_statistics']['success_rate']:.1f}%",
                        'trend': 'up' if metrics['job_statistics']['success_rate'] > 90 else 'down',
                        'color': 'green' if metrics['job_statistics']['success_rate'] > 90 else 'red'
                    },
                    {
                        'name': 'Avg Processing Time',
                        'value': f"{metrics['performance']['average_processing_time']:.1f}s",
                        'trend': 'stable',
                        'color': 'yellow'
                    },
                    {
                        'name': 'Files Processed Today',
                        'value': metrics['job_statistics']['files_processed_today'],
                        'trend': 'up',
                        'color': 'green'
                    }
                ],
                
                # Status indicators
                'health_indicators': [
                    {
                        'name': 'Task Queue',
                        'status': metrics['system_health']['task_queue_status'],
                        'message': 'Dramatiq queue operational'
                    },
                    {
                        'name': 'Database',
                        'status': metrics['system_health']['database_status'],
                        'message': 'Repository accessible'
                    },
                    {
                        'name': 'Redis Cache',
                        'status': metrics['system_health']['redis_status'],
                        'message': 'Real-time tracking active'
                    }
                ],
                
                # Recent activity
                'recent_activity': self._format_recent_activity(
                    metrics['active_jobs_summary']['recent_jobs']
                ),
                
                # Alerts and warnings
                'alerts': await self._generate_alerts(metrics)
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get dashboard data: {e}")
            return self._get_error_dashboard_data(str(e))
    
    async def perform_health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check
        
        Returns:
            Health check results
        """
        try:
            logger.debug("[PTS-MONITOR] Performing PTS health check")
            
            health_status = {
                'service': 'pts_renamer',
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'checks': []
            }
            
            # Check repository connectivity
            try:
                jobs = await self.repository.get_job_history(1)
                health_status['checks'].append({
                    'check': 'repository_connectivity',
                    'status': 'pass',
                    'message': 'Repository accessible'
                })
            except Exception as e:
                health_status['checks'].append({
                    'check': 'repository_connectivity',
                    'status': 'fail',
                    'message': f'Repository error: {e}'
                })
                health_status['overall_status'] = 'unhealthy'
            
            # Check Dramatiq task availability
            try:
                from backend.tasks.dramatiq_integration import is_dramatiq_available
                if is_dramatiq_available():
                    health_status['checks'].append({
                        'check': 'dramatiq_availability',
                        'status': 'pass',
                        'message': 'Dramatiq tasks available'
                    })
                else:
                    health_status['checks'].append({
                        'check': 'dramatiq_availability',
                        'status': 'fail',
                        'message': 'Dramatiq not available'
                    })
                    health_status['overall_status'] = 'degraded'
            except Exception as e:
                health_status['checks'].append({
                    'check': 'dramatiq_availability',
                    'status': 'fail',
                    'message': f'Dramatiq check failed: {e}'
                })
                health_status['overall_status'] = 'unhealthy'
            
            # Check Redis connectivity
            try:
                if self.redis_client:
                    self.redis_client.ping()
                    health_status['checks'].append({
                        'check': 'redis_connectivity',
                        'status': 'pass',
                        'message': 'Redis accessible'
                    })
                else:
                    health_status['checks'].append({
                        'check': 'redis_connectivity',
                        'status': 'warn',
                        'message': 'Redis client not available'
                    })
            except Exception as e:
                health_status['checks'].append({
                    'check': 'redis_connectivity',
                    'status': 'fail',
                    'message': f'Redis error: {e}'
                })
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'degraded'
            
            # Check for stuck jobs
            stuck_jobs = await self._detect_stuck_jobs()
            if stuck_jobs:
                health_status['checks'].append({
                    'check': 'stuck_jobs_detection',
                    'status': 'warn',
                    'message': f'Found {len(stuck_jobs)} potentially stuck jobs'
                })
                health_status['stuck_jobs'] = stuck_jobs
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'degraded'
            else:
                health_status['checks'].append({
                    'check': 'stuck_jobs_detection',
                    'status': 'pass',
                    'message': 'No stuck jobs detected'
                })
            
            logger.debug(f"[PTS-MONITOR] Health check completed: {health_status['overall_status']}")
            return health_status
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Health check failed: {e}")
            return {
                'service': 'pts_renamer',
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'error',
                'error': str(e)
            }
    
    # Private helper methods
    
    def _calculate_job_statistics(self, active_jobs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate job statistics from active jobs"""
        try:
            total_jobs_today = len([
                job for job in active_jobs 
                if self._is_today(job.get('created_at', ''))
            ])
            
            completed_jobs = len([
                job for job in active_jobs 
                if job.get('status') == 'completed'
            ])
            
            success_rate = (completed_jobs / len(active_jobs) * 100) if active_jobs else 100
            
            files_processed_today = sum([
                job.get('metrics', {}).get('processed_files', 0)
                for job in active_jobs
                if self._is_today(job.get('created_at', ''))
            ])
            
            return {
                'active_jobs': len(active_jobs),
                'total_jobs_today': total_jobs_today,
                'completed_jobs': completed_jobs,
                'success_rate': success_rate,
                'files_processed_today': files_processed_today
            }
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to calculate job statistics: {e}")
            return {
                'active_jobs': 0,
                'total_jobs_today': 0,
                'completed_jobs': 0,
                'success_rate': 0,
                'files_processed_today': 0
            }
    
    async def _get_system_health(self) -> Dict[str, Any]:
        """Get system health status"""
        try:
            return {
                'task_queue_status': 'healthy',  # Would check Dramatiq
                'database_status': 'healthy',    # Would check repository
                'redis_status': 'healthy' if self.redis_client else 'degraded',
                'memory_usage': self._get_memory_usage(),
                'disk_usage': self._get_disk_usage()
            }
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get system health: {e}")
            return {
                'task_queue_status': 'unknown',
                'database_status': 'unknown',
                'redis_status': 'unknown',
                'memory_usage': 0,
                'disk_usage': 0
            }
    
    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        try:
            # This would calculate from historical data
            return {
                'average_processing_time': 45.2,  # seconds
                'throughput_per_hour': 120,       # files per hour
                'peak_concurrent_jobs': 5,
                'average_file_size': 2.5          # MB
            }
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get performance metrics: {e}")
            return {
                'average_processing_time': 0,
                'throughput_per_hour': 0,
                'peak_concurrent_jobs': 0,
                'average_file_size': 0
            }
    
    async def _get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        try:
            # This would analyze error patterns
            return {
                'total_errors_today': 2,
                'error_rate': 1.5,  # percentage
                'common_errors': [
                    {'type': 'FileNotFound', 'count': 1},
                    {'type': 'ValidationError', 'count': 1}
                ],
                'retry_success_rate': 85.0
            }
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to get error statistics: {e}")
            return {
                'total_errors_today': 0,
                'error_rate': 0,
                'common_errors': [],
                'retry_success_rate': 0
            }
    
    def _group_jobs_by_status(self, jobs: List[Dict[str, Any]]) -> Dict[str, int]:
        """Group jobs by status"""
        status_counts = {}
        for job in jobs:
            status = job.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        return status_counts
    
    def _format_recent_activity(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format recent activity for dashboard"""
        return [
            {
                'job_id': job['job_id'][:12],  # Truncate for display
                'status': job.get('status', 'unknown'),
                'progress': job.get('progress', 0),
                'updated_at': job.get('updated_at', ''),
                'message': job.get('message', 'Processing...')
            }
            for job in jobs[:5]  # Limit to 5 most recent
        ]
    
    async def _generate_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts based on metrics"""
        alerts = []
        
        try:
            # Check success rate
            success_rate = metrics['job_statistics']['success_rate']
            if success_rate < 90:
                alerts.append({
                    'level': 'warning',
                    'message': f'Low success rate: {success_rate:.1f}%',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Check for stuck jobs
            stuck_jobs = await self._detect_stuck_jobs()
            if stuck_jobs:
                alerts.append({
                    'level': 'warning',
                    'message': f'{len(stuck_jobs)} jobs may be stuck',
                    'timestamp': datetime.now().isoformat()
                })
            
            # Check system health
            if metrics['system_health']['task_queue_status'] != 'healthy':
                alerts.append({
                    'level': 'error',
                    'message': 'Task queue not healthy',
                    'timestamp': datetime.now().isoformat()
                })
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to generate alerts: {e}")
            alerts.append({
                'level': 'error',
                'message': f'Alert generation failed: {e}',
                'timestamp': datetime.now().isoformat()
            })
        
        return alerts
    
    async def _detect_stuck_jobs(self) -> List[str]:
        """Detect potentially stuck jobs"""
        try:
            stuck_jobs = []
            active_jobs = await self.monitor.get_active_jobs()
            
            cutoff_time = datetime.now() - timedelta(hours=2)
            
            for job in active_jobs:
                updated_at = job.get('updated_at')
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at)
                        if last_update < cutoff_time and job.get('status') not in ['completed', 'failed']:
                            stuck_jobs.append(job['job_id'])
                    except ValueError:
                        continue  # Skip invalid timestamps
            
            return stuck_jobs
            
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to detect stuck jobs: {e}")
            return []
    
    def _is_today(self, timestamp_str: str) -> bool:
        """Check if timestamp is from today"""
        try:
            if not timestamp_str:
                return False
            timestamp = datetime.fromisoformat(timestamp_str)
            return timestamp.date() == datetime.now().date()
        except (ValueError, TypeError):
            return False
    
    def _get_memory_usage(self) -> float:
        """Get memory usage percentage"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            return 0.0
    
    def _get_disk_usage(self) -> float:
        """Get disk usage percentage"""
        try:
            import psutil
            return psutil.disk_usage('/').percent
        except (ImportError, FileNotFoundError):
            return 0.0
    
    async def _store_metrics(self, metrics: Dict[str, Any]):
        """Store metrics in Redis"""
        if not self.redis_client:
            return
        
        try:
            metrics_key = f"{self.metrics_key_prefix}:current"
            self.redis_client.setex(
                metrics_key, self.metrics_retention,
                json.dumps(metrics, default=str)
            )
        except Exception as e:
            logger.error(f"[PTS-MONITOR] Failed to store metrics: {e}")
    
    def _get_error_metrics(self, error: str) -> Dict[str, Any]:
        """Get error metrics when collection fails"""
        return {
            'service_name': 'PTS File Renamer',
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'error': error,
            'job_statistics': {
                'active_jobs': 0,
                'total_jobs_today': 0,
                'completed_jobs': 0,
                'success_rate': 0,
                'files_processed_today': 0
            }
        }
    
    def _get_error_dashboard_data(self, error: str) -> Dict[str, Any]:
        """Get error dashboard data when collection fails"""
        return {
            'service_id': 'pts_renamer',
            'service_name': 'PTS File Renamer',
            'status': 'error',
            'last_updated': datetime.now().isoformat(),
            'error': error,
            'kpis': [],
            'health_indicators': [],
            'recent_activity': [],
            'alerts': [{
                'level': 'error',
                'message': f'Monitoring collection failed: {error}',
                'timestamp': datetime.now().isoformat()
            }]
        }


# Integration functions for monitoring dashboard

async def register_pts_monitoring():
    """Register PTS monitoring with dashboard coordinator"""
    try:
        if MONITORING_AVAILABLE:
            # This would register PTS monitoring with the dashboard
            logger.info("[PTS-MONITOR] PTS monitoring registered with dashboard")
            return True
        else:
            logger.warning("[PTS-MONITOR] Dashboard monitoring not available")
            return False
    except Exception as e:
        logger.error(f"[PTS-MONITOR] Failed to register PTS monitoring: {e}")
        return False


async def get_pts_dashboard_metrics() -> Dict[str, Any]:
    """Get PTS metrics for dashboard (external interface)"""
    try:
        # This would be called by the monitoring dashboard
        collector = PTSMonitoringCollector(None, None)  # Dependencies would be injected
        return await collector.get_dashboard_data()
    except Exception as e:
        logger.error(f"[PTS-MONITOR] Failed to get dashboard metrics: {e}")
        return {
            'service_id': 'pts_renamer',
            'service_name': 'PTS File Renamer',
            'status': 'error',
            'error': str(e),
            'last_updated': datetime.now().isoformat()
        }


# Export monitoring classes and functions
__all__ = [
    'PTSMonitoringCollector',
    'register_pts_monitoring',
    'get_pts_dashboard_metrics'
]