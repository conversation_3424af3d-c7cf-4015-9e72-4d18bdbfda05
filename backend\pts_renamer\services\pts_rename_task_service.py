"""
PTS Rename Task Service

This module provides a high-level interface for submitting and managing
PTS rename tasks using the Dramatiq infrastructure. It acts as a bridge
between the PTS rename services and the task queue system.

Features:
- Easy task submission with validation
- Task status monitoring and progress tracking
- Integration with existing monitoring dashboard
- Error handling and retry management
- Cleanup and maintenance operations
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from loguru import logger

# Import PTS models and services
from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSProcessingStatus
)
from ..services.pts_rename_job_monitor import PTSRenameJobMonitor, PTSJobPhase
from ..repositories.pts_rename_repository import IPTSRenameRepository

# Import Dramatiq integration
from backend.tasks.dramatiq_integration import (
    get_dramatiq_task,
    get_task_status,
    is_dramatiq_available
)


class PTSRenameTaskService:
    """
    High-level service for managing PTS rename tasks
    
    This service provides a clean interface for:
    - Submitting PTS rename jobs to Dramatiq
    - Monitoring job progress and status
    - Managing retries and error handling
    - Cleaning up completed jobs
    """
    
    def __init__(self, 
                 repository: IPTSRenameRepository,
                 monitor: PTSRenameJobMonitor):
        """
        Initialize PTS task service
        
        Args:
            repository: PTS repository for job data
            monitor: Job monitoring service
        """
        self.repository = repository
        self.monitor = monitor
        
    async def submit_pts_rename_job(self, 
                                   job_request: PTSRenameJobRequest) -> Dict[str, Any]:
        """
        Submit a PTS rename job to the task queue
        
        Args:
            job_request: Job configuration and requirements
            
        Returns:
            Dict containing job_id, task_id, and submission status
            
        Raises:
            ValueError: If request validation fails
            RuntimeError: If Dramatiq is not available
        """
        try:
            logger.info(f"[PTS-TASK] Submitting PTS rename job for upload: {job_request.upload_id}")
            
            # Validate request
            await self._validate_job_request(job_request)
            
            # Check if Dramatiq is available
            if not is_dramatiq_available():
                raise RuntimeError("Dramatiq task queue is not available")
            
            # Generate job ID
            job_id = self._generate_job_id()
            
            # Prepare job data for task queue
            job_data = self._serialize_job_request(job_request, job_id)
            
            # Get PTS rename task
            pts_task = get_dramatiq_task('process_pts_rename_job')
            if not pts_task:
                # Try to get from PTS module directly
                from ..services.pts_rename_dramatiq_integration import process_pts_rename_job_task
                pts_task = process_pts_rename_job_task
            
            if not pts_task:
                raise RuntimeError("PTS rename task is not available")
            
            # Submit task to queue
            task_message = pts_task.send(job_id, job_data)
            task_id = task_message.message_id
            
            # Start job tracking
            await self.monitor.start_job_tracking(job_id, task_id, job_data)
            
            logger.info(f"[PTS-TASK] Job submitted successfully: job_id={job_id}, task_id={task_id}")
            
            return {
                'job_id': job_id,
                'task_id': task_id,
                'status': 'submitted',
                'message': 'Job submitted to queue successfully',
                'submitted_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to submit job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Optional[PTSRenameJobStatus]:
        """
        Get comprehensive job status
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job status with progress details or None if not found
        """
        try:
            # Get status from monitor first (real-time)
            status_data = await self.monitor.get_job_status(job_id)
            
            if status_data:
                return PTSRenameJobStatus(
                    job_id=job_id,
                    status=PTSProcessingStatus(status_data.get('status', 'pending')),
                    progress=status_data.get('progress', 0),
                    files_processed=status_data.get('metrics', {}).get('processed_files', 0),
                    total_files=status_data.get('metrics', {}).get('total_files', 0),
                    error_message=status_data.get('error'),
                    result_download_url=status_data.get('download_url'),
                    created_at=datetime.fromisoformat(status_data['created_at']) if status_data.get('created_at') else None,
                    updated_at=datetime.fromisoformat(status_data['updated_at']) if status_data.get('updated_at') else None
                )
            
            # Fallback to repository
            job = await self.repository.get_job(job_id)
            if job:
                return PTSRenameJobStatus(
                    job_id=job.job_id,
                    status=job.status,
                    progress=getattr(job, 'progress_percentage', 0),
                    files_processed=getattr(job, 'files_processed', 0),
                    total_files=job.total_files,
                    error_message=job.error_message,
                    result_download_url=job.download_url,
                    created_at=job.created_at,
                    updated_at=job.updated_at
                )
            
            return None
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to get job status: {e}")
            return None
    
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a running or queued job
        
        Args:
            job_id: Job identifier
            
        Returns:
            Success status
        """
        try:
            logger.info(f"[PTS-TASK] Cancelling job: {job_id}")
            
            # Update job status in repository
            job = await self.repository.get_job(job_id)
            if job:
                job.cancel("Job cancelled by user request")
                await self.repository.save_job(job)
            
            # Update monitoring
            await self.monitor.fail_job_tracking(
                job_id, "", "Job cancelled by user", "UserCancellation", False
            )
            
            logger.info(f"[PTS-TASK] Job cancelled successfully: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to cancel job: {e}")
            return False
    
    async def retry_failed_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Retry a failed job
        
        Args:
            job_id: Job identifier
            
        Returns:
            New job submission details or None if retry not possible
        """
        try:
            logger.info(f"[PTS-TASK] Retrying failed job: {job_id}")
            
            # Get original job
            job = await self.repository.get_job(job_id)
            if not job:
                logger.error(f"[PTS-TASK] Job not found for retry: {job_id}")
                return None
            
            if job.status != PTSProcessingStatus.FAILED:
                logger.warning(f"[PTS-TASK] Job is not in failed state: {job_id}")
                return None
            
            # Create new job request based on original
            retry_request = PTSRenameJobRequest(
                upload_id=job.upload_id,
                operations=job.operations,
                rename_config=job.rename_config,
                qc_enabled=job.qc_enabled,
                create_directories=job.create_directories
            )
            
            # Submit new job
            result = await self.submit_pts_rename_job(retry_request)
            
            # Record retry relationship
            await self.monitor.handle_job_retry(
                job_id, "", result['task_id'], 1
            )
            
            logger.info(f"[PTS-TASK] Job retry submitted: {result['job_id']}")
            return result
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to retry job: {e}")
            return None
    
    async def submit_compression_task(self, 
                                     source_path: str, 
                                     job_id: str,
                                     archive_name: str = None) -> Optional[str]:
        """
        Submit compression task for processed files
        
        Args:
            source_path: Path to compress
            job_id: Associated job ID
            archive_name: Optional archive name
            
        Returns:
            Task ID or None if submission failed
        """
        try:
            logger.info(f"[PTS-TASK] Submitting compression task for job: {job_id}")
            
            # Get compression task
            compression_task = get_dramatiq_task('pts_file_compression')
            if not compression_task:
                from ..services.pts_rename_dramatiq_integration import pts_file_compression_task
                compression_task = pts_file_compression_task
            
            if not compression_task:
                logger.error("[PTS-TASK] Compression task not available")
                return None
            
            # Submit compression task
            task_message = compression_task.send(source_path, archive_name, job_id)
            task_id = task_message.message_id
            
            logger.info(f"[PTS-TASK] Compression task submitted: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to submit compression task: {e}")
            return None
    
    async def submit_cleanup_task(self, 
                                 job_id: str,
                                 cleanup_paths: List[str] = None) -> Optional[str]:
        """
        Submit cleanup task for expired jobs and files
        
        Args:
            job_id: Job identifier
            cleanup_paths: Specific paths to clean
            
        Returns:
            Task ID or None if submission failed
        """
        try:
            logger.info(f"[PTS-TASK] Submitting cleanup task for job: {job_id}")
            
            # Get cleanup task
            cleanup_task = get_dramatiq_task('pts_cleanup')
            if not cleanup_task:
                from ..services.pts_rename_dramatiq_integration import pts_cleanup_task
                cleanup_task = pts_cleanup_task
            
            if not cleanup_task:
                logger.error("[PTS-TASK] Cleanup task not available")
                return None
            
            # Submit cleanup task
            task_message = cleanup_task.send(job_id, cleanup_paths)
            task_id = task_message.message_id
            
            logger.info(f"[PTS-TASK] Cleanup task submitted: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to submit cleanup task: {e}")
            return None
    
    async def get_active_jobs(self) -> List[Dict[str, Any]]:
        """
        Get list of active jobs
        
        Returns:
            List of active job information
        """
        try:
            return await self.monitor.get_active_jobs()
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to get active jobs: {e}")
            return []
    
    async def get_job_history(self, 
                             limit: int = 50,
                             status_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get job processing history
        
        Args:
            limit: Maximum number of jobs to return
            status_filter: Optional status filter
            
        Returns:
            List of historical job information
        """
        try:
            return await self.monitor.get_job_history(limit, status_filter)
        except Exception as e:
            logger.error(f"[PTS-TASK] Failed to get job history: {e}")
            return []
    
    # Private helper methods
    
    async def _validate_job_request(self, request: PTSRenameJobRequest):
        """Validate job request"""
        if not request.upload_id:
            raise ValueError("Upload ID is required")
        
        if not request.operations:
            raise ValueError("At least one operation must be specified")
        
        # Check if upload exists and has PTS files
        pts_files = await self.repository.get_pts_files(request.upload_id)
        if not pts_files:
            raise ValueError(f"No PTS files found for upload {request.upload_id}")
    
    def _generate_job_id(self) -> str:
        """Generate unique job ID"""
        return f"pts_job_{uuid.uuid4().hex[:12]}"
    
    def _serialize_job_request(self, request: PTSRenameJobRequest, job_id: str) -> Dict[str, Any]:
        """Serialize job request for task queue"""
        return {
            'job_id': job_id,
            'upload_id': request.upload_id,
            'operations': [op.value for op in request.operations],
            'rename_config': request.rename_config,
            'qc_enabled': request.qc_enabled,
            'create_directories': request.create_directories,
            'submitted_at': datetime.now().isoformat()
        }


# Convenience functions for external use

async def submit_pts_job(job_request: PTSRenameJobRequest,
                        repository: IPTSRenameRepository,
                        monitor: PTSRenameJobMonitor) -> Dict[str, Any]:
    """
    Convenience function to submit PTS job
    
    Args:
        job_request: Job request
        repository: PTS repository
        monitor: Job monitor
        
    Returns:
        Submission result
    """
    service = PTSRenameTaskService(repository, monitor)
    return await service.submit_pts_rename_job(job_request)


async def get_pts_job_status(job_id: str,
                            repository: IPTSRenameRepository,
                            monitor: PTSRenameJobMonitor) -> Optional[PTSRenameJobStatus]:
    """
    Convenience function to get job status
    
    Args:
        job_id: Job identifier
        repository: PTS repository
        monitor: Job monitor
        
    Returns:
        Job status or None
    """
    service = PTSRenameTaskService(repository, monitor)
    return await service.get_job_status(job_id)


# Export main service and convenience functions
__all__ = [
    'PTSRenameTaskService',
    'submit_pts_job',
    'get_pts_job_status'
]