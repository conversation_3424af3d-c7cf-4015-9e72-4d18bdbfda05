"""
統一監控儀表板 - 系統監控收集器

此模組實現系統資源和服務健康狀態監控，包括：
- 系統資源指標收集 (CPU、記憶體、磁碟)
- 服務健康狀態檢查
- 資料庫效能監控
- 網路連接監控

需求對應：
- 需求 6: 系統健康監控和效能指標
- 需求 7: 郵件服務和資料庫連線狀態監控
"""

import os
import sys
import time
import psutil
import sqlite3
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from ..models.dashboard_metrics_models import SystemMetrics, ServiceHealth
from ..utils.dashboard_helpers import safe_execute, log_performance


class DashboardSystemCollector:
    """系統監控收集器 - 收集系統資源和服務狀態"""
    
    def __init__(self, logger=None):
        """初始化系統監控收集器"""
        self.logger = logger
        self._lock = threading.Lock()
        
        # 系統資訊快取
        self._last_network_io = None
        self._last_network_time = None
        
        # 服務檢查配置
        self.service_check_timeout = 5.0  # 秒
        # 支援多資料庫監控
        self.database_paths = {
            "outlook": "outlook.db",
            "pts_rename": "data/pts_rename.db"
        }
        # 保持向後兼容性的主資料庫路徑
        self.database_path = "outlook.db"
        
        # 效能統計
        self.collection_count = 0
        self.last_collection_time = None
        self.avg_collection_duration = 0.0
        
        if self.logger:
            self.logger.info("系統監控收集器已初始化")
    
    @safe_execute(default_return=SystemMetrics())
    @log_performance
    async def collect_system_metrics(self) -> SystemMetrics:
        """
        收集系統監控指標
        
        Returns:
            SystemMetrics: 系統指標資料
        """
        with self._lock:
            start_time = time.time()
            
            try:
                # 收集基本資源指標
                cpu_percent = self._get_cpu_usage()
                memory_info = self._get_memory_info()
                disk_info = self._get_disk_info()
                
                # 收集系統負載
                load_avg = self._get_load_average()
                
                # 收集網路和連接資訊
                network_info = self._get_network_info()
                connection_info = self._get_connection_info()
                
                # 收集服務健康狀態
                service_health = await self._check_service_health()
                
                # 收集資料庫指標
                db_metrics = await self._get_database_metrics()
                
                # 收集程序資訊
                process_info = self._get_process_info()
                
                # 建立系統指標物件
                metrics = SystemMetrics(
                    # 基本資源使用率
                    cpu_percent=cpu_percent,
                    memory_percent=memory_info['percent'],
                    disk_percent=disk_info['percent'],
                    
                    # 詳細資源資訊
                    memory_available_mb=memory_info['available_mb'],
                    memory_used_mb=memory_info['used_mb'],
                    disk_free_gb=disk_info['free_gb'],
                    disk_used_gb=disk_info['used_gb'],
                    
                    # 系統負載
                    load_average_1m=load_avg[0],
                    load_average_5m=load_avg[1],
                    load_average_15m=load_avg[2],
                    
                    # 網路和連接
                    active_connections=connection_info['active'],
                    websocket_connections=connection_info['websocket'],
                    network_io_bytes_sent=network_info['bytes_sent'],
                    network_io_bytes_recv=network_info['bytes_recv'],
                    
                    # 服務健康狀態
                    service_health=service_health,
                    
                    # 資料庫指標
                    database_connections=db_metrics['connections'],
                    database_query_avg_time_ms=db_metrics['avg_query_time'],
                    database_size_mb=db_metrics['size_mb'],
                    database_lock_count=db_metrics['lock_count'],
                    
                    # 程序資訊
                    process_count=process_info['process_count'],
                    thread_count=process_info['thread_count'],
                    uptime_seconds=process_info['uptime_seconds']
                )
                
                # 更新效能統計
                self._update_performance_stats(time.time() - start_time)
                
                if self.logger:
                    self.logger.debug(f"系統指標收集完成，耗時 {time.time() - start_time:.3f}s")
                
                return metrics
                
            except Exception as e:
                if self.logger:
                    self.logger.error(f"系統指標收集失敗: {e}")
                return SystemMetrics()
    
    def _get_cpu_usage(self) -> float:
        """獲取 CPU 使用率"""
        try:
            # 使用短時間間隔獲取更準確的 CPU 使用率
            return psutil.cpu_percent(interval=0.1)
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取 CPU 使用率失敗: {e}")
            return 0.0
    
    def _get_memory_info(self) -> Dict[str, float]:
        """獲取記憶體資訊"""
        try:
            memory = psutil.virtual_memory()
            return {
                'percent': memory.percent,
                'available_mb': memory.available / (1024 * 1024),
                'used_mb': memory.used / (1024 * 1024),
                'total_mb': memory.total / (1024 * 1024)
            }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取記憶體資訊失敗: {e}")
            return {'percent': 0.0, 'available_mb': 0.0, 'used_mb': 0.0, 'total_mb': 0.0}
    
    def _get_disk_info(self) -> Dict[str, float]:
        """獲取磁碟資訊"""
        try:
            # 獲取當前工作目錄的磁碟使用情況
            disk = psutil.disk_usage('.')
            return {
                'percent': (disk.used / disk.total) * 100,
                'free_gb': disk.free / (1024 * 1024 * 1024),
                'used_gb': disk.used / (1024 * 1024 * 1024),
                'total_gb': disk.total / (1024 * 1024 * 1024)
            }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取磁碟資訊失敗: {e}")
            return {'percent': 0.0, 'free_gb': 0.0, 'used_gb': 0.0, 'total_gb': 0.0}
    
    def _get_load_average(self) -> Tuple[float, float, float]:
        """獲取系統負載平均值"""
        try:
            if hasattr(os, 'getloadavg'):
                # Unix/Linux 系統
                return os.getloadavg()
            else:
                # Windows 系統使用 CPU 使用率作為替代
                cpu_count = psutil.cpu_count()
                cpu_percent = psutil.cpu_percent(interval=0.1)
                load_estimate = (cpu_percent / 100.0) * cpu_count
                return (load_estimate, load_estimate, load_estimate)
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取系統負載失敗: {e}")
            return (0.0, 0.0, 0.0)
    
    def _get_network_info(self) -> Dict[str, int]:
        """獲取網路 I/O 資訊"""
        try:
            current_time = time.time()
            net_io = psutil.net_io_counters()
            
            if self._last_network_io is None or self._last_network_time is None:
                # 第一次收集，返回累計值
                self._last_network_io = net_io
                self._last_network_time = current_time
                return {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv
                }
            
            # 計算速率 (每秒位元組數)
            time_delta = current_time - self._last_network_time
            if time_delta > 0:
                bytes_sent_rate = int((net_io.bytes_sent - self._last_network_io.bytes_sent) / time_delta)
                bytes_recv_rate = int((net_io.bytes_recv - self._last_network_io.bytes_recv) / time_delta)
            else:
                bytes_sent_rate = 0
                bytes_recv_rate = 0
            
            # 更新快取
            self._last_network_io = net_io
            self._last_network_time = current_time
            
            return {
                'bytes_sent': bytes_sent_rate,
                'bytes_recv': bytes_recv_rate
            }
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取網路資訊失敗: {e}")
            return {'bytes_sent': 0, 'bytes_recv': 0}
    
    def _get_connection_info(self) -> Dict[str, int]:
        """獲取連接資訊"""
        try:
            connections = psutil.net_connections()
            active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
            
            # 嘗試估算 WebSocket 連接數 (通常在特定端口)
            websocket_ports = [5555, 8010, 5000]  # 常見的 WebSocket 端口
            websocket_connections = 0
            
            for conn in connections:
                if (conn.laddr and conn.laddr.port in websocket_ports and 
                    conn.status == 'ESTABLISHED'):
                    websocket_connections += 1
            
            return {
                'active': active_connections,
                'websocket': websocket_connections,
                'total': len(connections)
            }
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取連接資訊失敗: {e}")
            return {'active': 0, 'websocket': 0, 'total': 0}
    
    async def _check_service_health(self) -> Dict[str, ServiceHealth]:
        """檢查服務健康狀態"""
        service_health = {}
        
        # 檢查郵件服務
        service_health['email_service'] = await self._check_email_service()
        
        # 檢查 Dramatiq 服務
        service_health['dramatiq_service'] = await self._check_dramatiq_service()
        
        # 檢查資料庫服務
        service_health['database'] = await self._check_database_service()
        
        # 檢查排程器服務
        service_health['scheduler'] = await self._check_scheduler_service()
        
        # 檢查 Redis 服務
        service_health['redis'] = await self._check_redis_service()
        
        return service_health
    
    async def _check_email_service(self) -> ServiceHealth:
        """檢查郵件服務健康狀態"""
        try:
            # 檢查郵件和PTS資料庫文件是否存在
            email_db_paths = ["email_inbox.db", "data/email_inbox.db", "outlook.db"]
            pts_db_paths = ["data/pts_rename.db"]
            
            # 合併所有需要檢查的資料庫路徑
            all_db_paths = email_db_paths + pts_db_paths
            db_exists = False
            healthy_dbs = 0
            
            for db_path in all_db_paths:
                if os.path.exists(db_path):
                    db_exists = True
                    try:
                        # 測試資料庫連接
                        conn = sqlite3.connect(db_path, timeout=2.0)
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                        result = cursor.fetchone()
                        conn.close()
                        
                        if result:
                            healthy_dbs += 1
                            if self.logger:
                                self.logger.debug(f"資料庫 {db_path} 連接成功")
                    except Exception as e:
                        if self.logger:
                            self.logger.debug(f"資料庫 {db_path} 連接測試失敗: {e}")
                        continue
            
            # 如果有任何資料庫正常，則返回健康狀態
            if healthy_dbs > 0:
                return ServiceHealth.HEALTHY
            
            # 如果資料庫文件存在但連接失敗
            if db_exists:
                return ServiceHealth.WARNING
            
            # 嘗試導入郵件相關模組
            try:
                from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
                return ServiceHealth.WARNING  # 模組存在但資料庫不可用
            except ImportError:
                return ServiceHealth.ERROR  # 模組不存在
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"郵件服務檢查失敗: {e}")
            return ServiceHealth.ERROR
    
    async def _check_dramatiq_service(self) -> ServiceHealth:
        """檢查 Dramatiq 服務健康狀態"""
        try:
            # 首先檢查 Redis 連接
            redis_status = await self._check_redis_connection()
            if redis_status != ServiceHealth.HEALTHY:
                return redis_status
            
            # 檢查 Dramatiq 進程是否在運行
            dramatiq_running = await self._check_dramatiq_processes()
            
            if dramatiq_running:
                return ServiceHealth.HEALTHY
            else:
                # Redis 可用但 Dramatiq 進程未運行
                return ServiceHealth.WARNING
            
        except ImportError:
            return ServiceHealth.WARNING
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Dramatiq 服務檢查失敗: {e}")
            return ServiceHealth.ERROR
    
    async def _check_redis_connection(self) -> ServiceHealth:
        """檢查 Redis 連接狀態"""
        try:
            import redis
            
            # 嘗試多個常見的 Redis 配置
            redis_configs = [
                {'host': 'localhost', 'port': 6379, 'db': 0},
                {'host': '127.0.0.1', 'port': 6379, 'db': 0},
                {'host': 'localhost', 'port': 6380, 'db': 0},  # 備用端口
            ]
            
            for config in redis_configs:
                try:
                    r = redis.Redis(socket_timeout=2, socket_connect_timeout=2, **config)
                    r.ping()
                    return ServiceHealth.HEALTHY
                except Exception:
                    continue
            
            return ServiceHealth.ERROR
            
        except ImportError:
            return ServiceHealth.WARNING
        except Exception:
            return ServiceHealth.ERROR
    
    async def _check_dramatiq_processes(self) -> bool:
        """檢查 Dramatiq 工作進程是否在運行"""
        try:
            import psutil
            
            # 檢查是否有 dramatiq 相關的進程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info.get('cmdline', [])
                    if cmdline and any('dramatiq' in arg.lower() for arg in cmdline):
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except ImportError:
            return False
        except Exception:
            return False
    
    async def _check_database_service(self) -> ServiceHealth:
        """檢查資料庫服務健康狀態 (支援多資料庫)"""
        healthy_count = 0
        warning_count = 0
        total_count = 0
        
        # 檢查所有配置的資料庫
        for db_name, db_path in self.database_paths.items():
            total_count += 1
            try:
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path, timeout=self.service_check_timeout)
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    conn.close()
                    healthy_count += 1
                    if self.logger:
                        self.logger.debug(f"資料庫 {db_name} ({db_path}) 健康檢查成功")
                else:
                    warning_count += 1
                    if self.logger:
                        self.logger.debug(f"資料庫文件不存在: {db_name} ({db_path})")
                        
            except Exception as e:
                warning_count += 1
                if self.logger:
                    self.logger.warning(f"資料庫 {db_name} ({db_path}) 檢查失敗: {e}")
        
        # 根據健康資料庫的比例返回狀態
        if healthy_count == total_count:
            return ServiceHealth.HEALTHY
        elif healthy_count > 0:
            return ServiceHealth.WARNING  # 部分資料庫健康
        else:
            return ServiceHealth.ERROR  # 所有資料庫都有問題
    
    async def _check_scheduler_service(self) -> ServiceHealth:
        """檢查排程器服務健康狀態"""
        try:
            # 嘗試導入排程器相關模組
            from backend.tasks.services.enhanced_task_scheduler import EnhancedTaskScheduler
            
            # 檢查是否有排程器實例在運行
            # 這裡可以添加更具體的健康檢查邏輯
            return ServiceHealth.HEALTHY
            
        except ImportError:
            return ServiceHealth.WARNING
        except Exception as e:
            if self.logger:
                self.logger.warning(f"排程器服務檢查失敗: {e}")
            return ServiceHealth.ERROR
    
    async def _check_redis_service(self) -> ServiceHealth:
        """檢查 Redis 服務健康狀態"""
        return await self._check_redis_connection()
    
    async def _get_database_metrics(self) -> Dict[str, Any]:
        """獲取資料庫效能指標 (支援多資料庫彙總)"""
        try:
            # 初始化彙總指標
            aggregated_metrics = {
                'connections': 0,
                'avg_query_time': 0.0,
                'size_mb': 0.0,
                'lock_count': 0,
                'database_count': 0,
                'database_details': {}  # 存儲各資料庫詳細資訊
            }
            
            total_query_time = 0.0
            query_count = 0
            
            # 遍歷所有配置的資料庫
            for db_name, db_path in self.database_paths.items():
                if os.path.exists(db_path):
                    try:
                        # 獲取資料庫檔案大小
                        file_size = os.path.getsize(db_path)
                        db_size_mb = file_size / (1024 * 1024)
                        
                        # 測試查詢回應時間
                        start_time = time.time()
                        conn = sqlite3.connect(db_path, timeout=self.service_check_timeout)
                        cursor = conn.cursor()
                        
                        # 執行簡單查詢
                        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                        result = cursor.fetchone()
                        
                        query_time = (time.time() - start_time) * 1000  # 轉換為毫秒
                        
                        # SQLite 鎖定檢查
                        lock_count = 0
                        try:
                            cursor.execute("BEGIN IMMEDIATE")
                            cursor.execute("ROLLBACK")
                        except sqlite3.OperationalError:
                            lock_count = 1
                        
                        conn.close()
                        
                        # 記錄單個資料庫的詳細資訊
                        aggregated_metrics['database_details'][db_name] = {
                            'path': db_path,
                            'size_mb': db_size_mb,
                            'query_time_ms': query_time,
                            'lock_count': lock_count,
                            'table_count': result[0] if result else 0
                        }
                        
                        # 累加到彙總指標
                        aggregated_metrics['connections'] += 1
                        aggregated_metrics['size_mb'] += db_size_mb
                        aggregated_metrics['lock_count'] += lock_count
                        total_query_time += query_time
                        query_count += 1
                        aggregated_metrics['database_count'] += 1
                        
                        if self.logger:
                            self.logger.debug(f"資料庫 {db_name} 指標收集成功: 大小 {db_size_mb:.2f}MB, 查詢時間 {query_time:.2f}ms")
                            
                    except Exception as e:
                        if self.logger:
                            self.logger.warning(f"資料庫 {db_name} ({db_path}) 指標收集失敗: {e}")
                        # 記錄失敗的資料庫
                        aggregated_metrics['database_details'][db_name] = {
                            'path': db_path,
                            'error': str(e),
                            'status': 'error'
                        }
                else:
                    if self.logger:
                        self.logger.debug(f"資料庫文件不存在: {db_name} ({db_path})")
                    # 記錄不存在的資料庫
                    aggregated_metrics['database_details'][db_name] = {
                        'path': db_path,
                        'status': 'not_found'
                    }
            
            # 計算平均查詢時間
            if query_count > 0:
                aggregated_metrics['avg_query_time'] = total_query_time / query_count
            
            return aggregated_metrics
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取資料庫指標失敗: {e}")
            return {
                'connections': 0,
                'avg_query_time': 0.0,
                'size_mb': 0.0,
                'lock_count': 0,
                'database_count': 0,
                'database_details': {},
                'error': str(e)
            }
    
    def _get_process_info(self) -> Dict[str, int]:
        """獲取程序資訊"""
        try:
            current_process = psutil.Process()
            
            # 獲取程序數量
            process_count = len(psutil.pids())
            
            # 獲取執行緒數量
            thread_count = current_process.num_threads()
            
            # 獲取運行時間
            create_time = current_process.create_time()
            uptime_seconds = int(time.time() - create_time)
            
            return {
                'process_count': process_count,
                'thread_count': thread_count,
                'uptime_seconds': uptime_seconds
            }
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"獲取程序資訊失敗: {e}")
            return {
                'process_count': 0,
                'thread_count': 0,
                'uptime_seconds': 0
            }
    
    def _update_performance_stats(self, duration: float) -> None:
        """更新效能統計"""
        self.collection_count += 1
        self.last_collection_time = datetime.now()
        
        # 計算平均收集時間 (簡單移動平均)
        if self.avg_collection_duration == 0:
            self.avg_collection_duration = duration
        else:
            self.avg_collection_duration = (self.avg_collection_duration * 0.9) + (duration * 0.1)
    
    def get_collector_stats(self) -> Dict[str, Any]:
        """獲取收集器統計資訊"""
        return {
            'collection_count': self.collection_count,
            'last_collection_time': self.last_collection_time.isoformat() if self.last_collection_time else None,
            'avg_collection_duration_ms': self.avg_collection_duration * 1000,
            'service_check_timeout': self.service_check_timeout,
            'database_path': self.database_path,  # 保持向後兼容
            'database_paths': self.database_paths,
            'monitored_databases': list(self.database_paths.keys())
        }
    
    def is_healthy(self) -> bool:
        """檢查收集器是否健康"""
        try:
            # 檢查 psutil 是否可用
            psutil.cpu_percent()
            return True
        except Exception:
            return False


# 工廠函數
def get_system_collector(logger=None) -> DashboardSystemCollector:
    """獲取系統監控收集器實例"""
    return DashboardSystemCollector(logger=logger)


