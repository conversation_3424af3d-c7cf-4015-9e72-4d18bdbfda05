# Epic-01 Database Infrastructure - 生產環境部署指南

## 📋 概述

本文檔提供 Epic-01 Database Infrastructure 在生產環境的完整部署指導，包含所有必要的安全檢查、備份程序、遷移執行和驗證步驟。

### 🎯 部署目標

Epic-01 為郵件系統新增以下功能：
- **Story 1.1**: 郵件狀態追蹤 (download_success, processing_success 等欄位)
- **Story 1.3**: 重試記錄系統 (email_download_retry_log 表)
- **Story 1.2**: 下載狀態優化 (進度追蹤、檔案大小等欄位)

### 📊 部署統計

- **遷移腳本數量**: 3 個
- **預估執行時間**: 2-5 分鐘
- **預估停機時間**: < 10 分鐘
- **風險等級**: 低-中等
- **可回滾性**: 是

---

## 🛠 準備工作

### 環境要求

- **Python 版本**: 3.8+
- **Alembic 版本**: 1.7+
- **SQLite 版本**: 3.35+
- **磁盤空間**: 至少 3GB 可用空間
- **權限**: 資料庫檔案讀寫權限

### 部署前檢查清單

**技術檢查**
- [ ] 確認資料庫檔案可正常訪問
- [ ] 驗證備份目錄有足夠空間
- [ ] 檢查 Python 環境和依賴套件
- [ ] 確認 Alembic 配置正確
- [ ] 驗證遷移腳本完整性

**業務檢查**
- [ ] 安排維護窗口 (建議非營業時間)
- [ ] 通知相關用戶和團隊
- [ ] 確認回滾計劃和責任人
- [ ] 準備緊急聯絡方式

**運維檢查**
- [ ] 停止相關應用服務
- [ ] 監控系統正常運行
- [ ] 備份當前資料庫
- [ ] 準備日誌監控

---

## 🚀 部署執行步驟

### 步驟 1: 自動化驗證和部署

我們強烈建議使用自動化腳本進行部署：

```bash
# 1. 進入項目目錄
cd /path/to/outlook_summary

# 2. 停止應用服務
sudo systemctl stop outlook-summary

# 3. 執行乾跑測試
python scripts/run_epic01_migration.py --dry-run

# 4. 如果乾跑成功，執行實際遷移
python scripts/run_epic01_migration.py --auto-rollback

# 5. 重啟應用服務
sudo systemctl start outlook-summary
```

### 步驟 2: 手動部署 (備用方案)

如果自動化腳本不可用，可以手動執行：

#### 2.1 預備作業

```bash
# 停止服務
sudo systemctl stop outlook-summary

# 創建手動備份
timestamp=$(date +%Y%m%d_%H%M%S)
cp data/email_inbox.db "backups/manual/pre_epic01_backup_$timestamp.db"

# 驗證備份
sqlite3 "backups/manual/pre_epic01_backup_$timestamp.db" "SELECT COUNT(*) FROM emails;"
```

#### 2.2 執行遷移

```bash
# 檢查當前 Alembic 狀態
alembic current

# 執行遷移 (按順序)
alembic upgrade 001_epic01_emails_ext
alembic upgrade 002_epic01_retry_log  
alembic upgrade 003_epic01_status_opt

# 驗證遷移結果
alembic current
```

#### 2.3 驗證和啟動

```bash
# 資料庫完整性檢查
sqlite3 data/email_inbox.db "PRAGMA integrity_check;"

# 檢查新表和欄位
sqlite3 data/email_inbox.db ".schema emails"
sqlite3 data/email_inbox.db ".schema email_download_retry_log"

# 重啟服務
sudo systemctl start outlook-summary

# 健康檢查
curl -f http://localhost:5000/health
```

---

## ✅ 部署驗證

### 自動化驗證

```bash
# 運行完整驗證套件
python scripts/validate_epic01_migrations.py --mode full

# 運行應用層測試
python -m pytest tests/integration/test_database_integration.py -v

# 檢查備份完整性
python scripts/database_backup_automation.py --status
```

### 手動驗證檢查項目

#### 資料庫結構驗證

```sql
-- 檢查 emails 表新欄位
PRAGMA table_info(emails);

-- 驗證新欄位預設值
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN download_success THEN 1 ELSE 0 END) as download_success_count,
    SUM(CASE WHEN processing_success THEN 1 ELSE 0 END) as processing_success_count
FROM emails;

-- 檢查重試記錄表
SELECT name FROM sqlite_master WHERE type='table' AND name='email_download_retry_log';

-- 驗證索引創建
.indices emails
.indices email_download_retry_log
```

#### 功能驗證

```bash
# 測試郵件處理功能
curl -X POST http://localhost:5000/api/emails/sync

# 檢查新功能 API
curl http://localhost:5000/api/emails/download-status

# 驗證重試機制
curl -X POST http://localhost:5000/api/emails/retry/123
```

#### 性能驗證

```bash
# 監控查詢性能
sqlite3 data/email_inbox.db ".timer on" "SELECT COUNT(*) FROM emails WHERE download_success = 1;"

# 檢查索引使用情況
sqlite3 data/email_inbox.db "EXPLAIN QUERY PLAN SELECT * FROM emails WHERE download_success = 1;"
```

---

## 🆘 回滾程序

### 自動回滾

如果使用了 `--auto-rollback` 選項，系統會在失敗時自動回滾。

### 手動回滾

#### 方法 1: Alembic 回滾

```bash
# 回滾到基準版本
alembic downgrade base

# 或回滾到特定版本
alembic downgrade 001_epic01_emails_ext
```

#### 方法 2: 備份恢復

```bash
# 停止服務
sudo systemctl stop outlook-summary

# 恢復備份
cp backups/manual/pre_epic01_backup_YYYYMMDD_HHMMSS.db data/email_inbox.db

# 驗證恢復
sqlite3 data/email_inbox.db "SELECT COUNT(*) FROM emails;"

# 重啟服務
sudo systemctl start outlook-summary
```

---

## 🔧 故障排除

### 常見問題

#### 問題 1: "table emails has no column named download_success"

**原因**: Story 1.1 遷移未完成
**解決方案**:
```bash
alembic upgrade 001_epic01_emails_ext
```

#### 問題 2: "no such table: email_download_retry_log"

**原因**: Story 1.3 遷移未完成
**解決方案**:
```bash
alembic upgrade 002_epic01_retry_log
```

#### 問題 3: "database is locked"

**原因**: 有程序正在使用資料庫
**解決方案**:
```bash
# 檢查佔用程序
lsof data/email_inbox.db

# 停止相關服務
sudo systemctl stop outlook-summary

# 重新嘗試遷移
```

#### 問題 4: 遷移腳本語法錯誤

**原因**: 腳本檔案損壞或版本不匹配
**解決方案**:
```bash
# 驗證腳本語法
python scripts/validate_epic01_migrations.py --mode syntax

# 重新下載或修復腳本檔案
```

### 緊急聯絡

| 情況 | 聯絡人 | 響應時間 |
|------|--------|----------|
| 遷移失敗 | DBA團隊 | 15分鐘內 |
| 資料損毀 | 系統管理員 | 立即 |
| 性能問題 | 開發團隊 | 1小時內 |

---

## 📊 監控和維護

### 部署後監控 (第一週)

**每小時檢查**:
- 應用服務狀態
- 資料庫連接狀況
- 錯誤日誌
- 系統資源使用

**每日檢查**:
- 備份狀態
- 新功能使用情況
- 性能指標
- 用戶反饋

### 長期維護

**每週**:
- 檢查新欄位使用情況
- 分析重試記錄統計
- 清理舊備份檔案

**每月**:
- 性能基準測試
- 索引使用率分析
- 容量規劃評估

---

## 📈 成功指標

### 技術指標

- **遷移成功率**: 100%
- **資料完整性**: 無資料遺失
- **性能影響**: < 10% 響應時間增加
- **錯誤率**: < 1%

### 業務指標

- **功能可用性**: 新狀態追蹤功能正常
- **重試機制**: 失敗郵件自動重試
- **用戶體驗**: 無明顯功能中斷

---

## 📚 相關資源

### 文檔連結

- [Epic-01 Technical Specification](docs/epics/epic-01-database-infrastructure.md)
- [Database Schema Documentation](docs/architecture/database-schema.md)
- [API Documentation](docs/api/email-api.md)

### 腳本和工具

- **遷移執行**: `scripts/run_epic01_migration.py`
- **驗證工具**: `scripts/validate_epic01_migrations.py`
- **備份工具**: `scripts/database_backup_automation.py`
- **健康檢查**: `scripts/database_health_monitor.py`

### 支援資源

- **測試環境**: http://dev.company.com:5000
- **監控儀表板**: http://monitor.company.com/outlook-summary
- **日誌查看**: `/var/log/outlook-summary/`

---

## 📝 部署記錄模板

```markdown
# Epic-01 部署記錄

**部署日期**: YYYY-MM-DD HH:MM
**執行人員**: [姓名]
**環境**: Production

## 執行步驟
- [ ] 停止服務
- [ ] 創建備份
- [ ] 執行遷移
- [ ] 驗證結果
- [ ] 重啟服務

## 結果
- **遷移狀態**: 成功/失敗
- **執行時間**: X 分鐘
- **資料完整性**: 通過/失敗
- **功能驗證**: 通過/失敗

## 備註
[記錄任何特殊情況或問題]

## 簽名
執行人: ____________
審核人: ____________
```

---

**⚠️ 重要提醒**: 
1. 始終在測試環境先執行完整部署流程
2. 確保有最新的備份和回滾計劃
3. 部署期間保持與團隊的溝通
4. 遇到問題時優先考慮資料安全

祝部署順利！🎉