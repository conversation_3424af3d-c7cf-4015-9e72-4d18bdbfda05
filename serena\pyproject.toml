[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
name = "serena-agent"
version = "0.1.4"
description = ""
authors = [{ name = "Oraios AI", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11, <3.12"
classifiers = [
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Python :: 3.11",
]
dependencies = [
  "requests>=2.32.3,<3",
  "pyright>=1.1.396,<2",
  "overrides>=7.7.0,<8",
  "python-dotenv>=1.0.0, <2",
  "mcp==1.12.3",
  "flask>=3.0.0",
  "sensai-utils>=1.5.0",
  "pydantic>=2.10.6",
  "types-pyyaml>=6.0.12.20241230",
  "pyyaml>=6.0.2",
  "ruamel.yaml>=0.18.0",
  "jinja2>=3.1.6",
  "dotenv>=0.9.9",
  "pathspec>=0.12.1",
  "psutil>=7.0.0",
  "docstring_parser>=0.16",
  "joblib>=1.5.1",
  "tqdm>=4.67.1",
  "tiktoken>=0.9.0",
  "anthropic>=0.54.0",
]

[[tool.uv.index]]
name = "testpypi"
url = "https://test.pypi.org/simple/"
publish-url = "https://test.pypi.org/legacy/"
explicit = true

[project.scripts]
serena = "serena.cli:top_level"
serena-mcp-server = "serena.cli:start_mcp_server"
index-project = "serena.cli:index_project"        # deprecated

[project.license]
text = "MIT"

[project.optional-dependencies]
dev = [
  "black[jupyter]>=23.7.0",
  "jinja2",
  # In version 1.0.4 we get a NoneType error related to some config conversion (yml_analytics is None and should be a list)
  "mypy>=1.16.1",
  "poethepoet>=0.20.0",
  "pytest>=8.0.2",
  "pytest-xdist>=3.5.0",
  "ruff>=0.0.285",
  "toml-sort>=0.24.2",
  "types-pyyaml>=6.0.12.20241230",
  "syrupy>=4.9.1",
]
agno = ["agno>=1.2.6", "sqlalchemy>=2.0.40"]
google = ["google-genai>=1.8.0"]

[project.urls]
Homepage = "https://github.com/oraios/serena"

[tool.hatch.build.targets.wheel]
packages = ["src/serena", "src/interprompt", "src/solidlsp"]

[tool.black]
line-length = 140
target-version = ["py311"]
exclude = '''
/(
    src/solidlsp/language_servers/.*/static|src/multilspy
)/
'''

[tool.doc8]
max-line-length = 1000

[tool.mypy]
allow_redefinition = true
check_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
ignore_missing_imports = true
no_implicit_optional = true
pretty = true
show_error_codes = true
show_error_context = true
show_traceback = true
strict_equality = true
strict_optional = true
warn_no_return = true
warn_redundant_casts = true
warn_unreachable = true
warn_unused_configs = true
warn_unused_ignores = false
exclude = "^build/|^docs/"

[tool.poe.env]
PYDEVD_DISABLE_FILE_VALIDATION = "1"

[tool.poe.tasks]
# Uses PYTEST_MARKERS env var for default markers
# For custom markers, one can either adjust the env var or just use -m option in the command line,
# as the second -m option will override the first one.
test = "pytest test -vv -m \"${PYTEST_MARKERS:-not java and not rust}\""
_black_check = "black --check src scripts test"
_ruff_check = "ruff check src scripts test"
_black_format = "black src scripts test"
_ruff_format = "ruff check --fix src scripts test"
lint = ["_black_check", "_ruff_check"]
format = ["_ruff_format", "_black_format"]
_mypy = "mypy src/serena"
type-check = ["_mypy"]

[tool.ruff]
target-version = "py311"
line-length = 140
exclude = ["src/solidlsp/language_servers/**/static", "src/multilspy"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
skip-magic-trailing-comma = false
docstring-code-format = true

[tool.ruff.lint]
select = [
  "ASYNC",
  "B",
  "C4",
  "C90",
  "COM",
  "D",
  "DTZ",
  "E",
  "F",
  "FLY",
  "G",
  "I",
  "ISC",
  "PIE",
  "PLC",
  "PLE",
  "PLW",
  "RET",
  "RUF",
  "RSE",
  "SIM",
  "TID",
  "UP",
  "W",
  "YTT",
]
ignore = [
  "PLC0415",
  "RUF002",
  "RUF005",
  "SIM118",
  "SIM108",
  "E501",
  "E741",
  "B008",
  "B011",
  "B028",
  "D100",
  "D101",
  "D102",
  "D103",
  "D104",
  "D105",
  "D107",
  "D200",
  "D203",
  "D213",
  "D401",
  "D402",
  "DTZ005",
  "E402",
  "E501",
  "E701",
  "E731",
  "C408",
  "E203",
  "G004",
  "RET505",
  "D106",
  "D205",
  "D212",
  "PLW2901",
  "B027",
  "D404",
  "D407",
  "D408",
  "D409",
  "D400",
  "D415",
  "COM812",
  "RET503",
  "RET504",
  "UP038",
  "F403",
  "F405",
  "C401",
  "C901",
  "ASYNC230",
  "ISC003",
  "B024",
  "B007",
  "SIM102",
  "W291",
  "W293",
  "B009",
  "SIM103",   # forbids multiple returns
  "SIM110",   # requires use of any(...) instead of for-loop
  "G001",     # forbids str.format in log statements
  "E722",     # forbids unspecific except clause
  "SIM105",   # forbids empty/general except clause
  "SIM113",   # wants to enforce use of enumerate
  "E712",     # forbids equality comparison with True/False
  "UP007",    # forbids some uses of Union
  "TID252",   # forbids relative imports
  "B904",     # forces use of raise from other_exception
  "RUF012",   # forbids mutable attributes as ClassVar
  "SIM117",   # forbids nested with statements
  "C400",     # wants to unnecessarily force use of list comprehension
  "UP037",    # can incorrectly (!) convert quoted type to unquoted type, causing an error
  "UP045",    # imposes T | None instead of Optional[T]
]
unfixable = ["F841", "F601", "F602", "B018"]
extend-fixable = ["F401", "B905", "W291"]

[tool.ruff.lint.mccabe]
max-complexity = 20

[tool.ruff.lint.per-file-ignores]
"tests/**" = ["D103"]
"scripts/**" = ["D103"]

[tool.pytest.ini_options]
addopts = "--snapshot-patch-pycharm-diff"
markers = [
  "clojure: language server running for Clojure",
  "python: language server running for Python",
  "go: language server running for Go",
  "java: language server running for Java",
  "rust: language server running for Rust",
  "typescript: language server running for TypeScript",
  "php: language server running for PHP",
  "csharp: language server running for C#",
  "elixir: language server running for Elixir",
  "terraform: language server running for Terraform",
  "swift: language server running for Swift",
  "bash: language server running for Bash",
  "snapshot: snapshot tests for symbolic editing operations",
  "ruby: language server running for Ruby",
]

[tool.codespell]
# Ref: https://github.com/codespell-project/codespell#using-a-config-file
skip = '.git*,*.svg,*.lock,*.min.*'
check-hidden = true
# ignore-regex = ''
# ignore-words-list = ''
