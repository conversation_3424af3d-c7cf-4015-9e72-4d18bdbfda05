# PTS Renamer - 基於規格的正確實作

## 規格分析總結
基於 .kiro/specs/pts-file-renamer-integration/ 和 .kiro/steering/pts-renamer.md 的完整分析：

### 關鍵發現
1. **MVP架構模式**: Model-View-Presenter，為Vue.js + FastAPI遷移做準備
2. **檔案命名規範**: 所有文件必須使用 `pts_rename` 前綴
3. **資料庫整合**: 使用現有的 `outlook.db` SQLite 資料庫
4. **Dramatiq整合**: 重用現有的 decompression, compression, batch_processing 任務
5. **網頁存取路徑**: `http://localhost:5000/pts-renamer/`

### 完成狀態確認
根據 .kiro/steering/pts-renamer.md：
- ✅ Task 1: 模組結構和核心接口 - 已完成
- ✅ Task 2: 核心資料模型和實體 - 已完成 
- ✅ Task 3: 檔案處理核心服務 - 已完成 (2025-01-20)
- ✅ Task 4: 上傳和檔案處理服務 - 已完成 (2025-08-20)
- 🔄 Task 5: Dramatiq整合和異步處理 - 待完成

### 需要的文件結構
```
backend/pts_renamer/
├── models/          # pts_rename_models.py, pts_rename_entities.py
├── services/        # pts_rename_processor.py, pts_rename_qc_generator.py
├── repositories/    # pts_rename_repository.py
└── api/            # pts_rename_fastapi_routes.py

frontend/pts_renamer/
├── routes/         # pts_rename_flask_routes.py
├── templates/      # pts_rename_main.html
└── static/         # pts_rename_upload.js, pts_rename_processor.js
```

### Task 5 實作要求
5.1 **Dramatiq任務整合服務** (pts_rename_dramatiq_integration.py):
- 使用現有 `processing_queue` 佇列
- 整合現有的 compression/decompression 任務
- 支援檔案批次處理

5.2 **工作狀態追蹤和監控**:
- 與現有監控儀表板整合
- 使用 `outlook.db` 資料庫儲存工作狀態
- 實作重試機制和錯誤處理

### 資料庫整合
擴展現有 `outlook.db` 新增表格：
- pts_rename_jobs (工作紀錄)
- pts_rename_files (檔案紀錄) 
- pts_rename_results (處理結果)

### 關鍵整合點
- 現有Dramatiq基礎設施 (`backend/tasks/`)
- 現有共享基礎設施 (`backend/shared/`)
- 現有監控系統整合
- SQLite 資料庫 (`outlook.db`)

## 實作策略
1. 先驗證Tasks 1-4的實際完成狀態
2. 檢查現有Dramatiq基礎設施
3. 基於真實存在的組件實作Task 5
4. 確保與現有系統完全整合
5. 在虛擬環境中進行完整測試