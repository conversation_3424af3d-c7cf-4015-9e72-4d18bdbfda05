# PTS File Renamer Integration - Design Document

## Overview

This document outlines the design for integrating PTS (Parametric Test System) file renaming functionality into the existing semiconductor email processing system using MVP (Model-View-Presenter) architecture. The design uses **unified database architecture** with `outlook.db` for simplified operations and prepares for future Vue.js + FastAPI migration while maintaining compatibility with the current Flask-based system.

### Key Architectural Decision: Unified Database

**Changed from**: Dedicated `pts_rename.db` → **To**: Unified `outlook.db` with `pts_rename_` table prefixes

**Operational Advantages**:
- ✅ **Single Database Management**: Simplified administration and maintenance
- ✅ **Unified Monitoring**: All components monitored through existing dashboard
- ✅ **Streamlined Backups**: One backup process covers all data
- ✅ **Reduced Configuration**: No additional database setup required
- ✅ **Better Integration**: Leverages existing database infrastructure
- ✅ **Simplified Deployment**: Uses established database deployment patterns

### Complete Feature Mapping from Existing Interface

Based on analysis of `reNameCTAF/rename_pts_files.py` and `pts_renamer_web.html`, all existing features are mapped to the new architecture:

| 現有功能 | 對應服務/組件 | 說明 |
|---------|-------------|------|
| **文件夾選擇** | `pts_rename_upload_service.py` | 支援多文件夾選擇，改為壓縮檔上傳 |
| **文件夾管理** (新增/移除/清空) | `pts_rename_upload_service.py` | Web 介面的檔案管理功能 |
| **重新命名** 選項 | `pts_rename_processor.py` | 完整的模式替換和正則表達式支援 |
| **增加QC** 選項 | `pts_rename_qc_generator.py` | QC 檔案生成，包含所有現有邏輯 |
| **創建目錄** 選項 | `pts_rename_directory_manager.py` | 目錄結構創建和檔案複製 |
| **替換前檔案名稱模式** | `pts_rename_processor.py` | 支援 `{old}`, `{ext}`, `{num}` 佔位符 |
| **替換後檔案名稱模式** | `pts_rename_processor.py` | 正則表達式和佔位符替換 |
| **輸出結果顯示** | `pts_rename_presenter.py` | 即時預覽和結果顯示 |
| **執行按鈕** | `pts_rename_flask_routes.py` | 處理執行和進度追蹤 |
| **QC 檔案特殊處理** | `pts_rename_qc_generator.py` | 完整實現現有 QC 邏輯 |
| **目錄複製邏輯** | `pts_rename_directory_manager.py` | 複製文件夾內容，排除其他 PTS 檔案 |
| **拖放上傳** (Web版) | `pts_rename_upload_service.py` | 支援拖放壓縮檔案上傳 |
| **進度顯示** (Web版) | `pts_rename_presenter.py` | 即時進度和狀態更新 |
| **結果下載** (Web版) | `pts_rename_download_service.py` | 自動壓縮和下載功能 |

### QC File Processing Details (完整對應現有邏輯)

現有 QC 處理的完整邏輯已對應到 `pts_rename_qc_generator.py`：

1. **移除 Parameter 到 QA 之間的資料** - `create_qc_file()`
2. **修改 QCOnlySBinAlter=1,0** - `modify_qc_content()`
3. **重新計算 ParamCnt** - `modify_qc_content()`
4. **處理 [Bin Definition] 部分** - `modify_qc_content()`
5. **只保留 bin 1 和 31** - `modify_qc_content()`
6. **生成 _QC 後綴檔名** - `create_qc_file()`

### Directory Creation Details (完整對應現有邏輯)

現有目錄創建的完整邏輯已對應到 `pts_rename_directory_manager.py`：

1. **檢查檔名與文件夾名稱衝突** - `create_pts_directory()`
2. **複製整個文件夾內容** - `copy_folder_contents()`
3. **刪除其他 PTS 檔案** - `copy_folder_contents()`
4. **刪除 INI 檔案** - `copy_folder_contents()`
5. **處理檔案衝突** - `create_pts_directory()`

**網頁存取路徑**: `http://localhost:5000/pts-renamer/`

## Architecture

### MVP Architecture Pattern

The system follows the MVP (Model-View-Presenter) pattern to ensure clean separation of concerns and facilitate future Vue.js + FastAPI migration:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      View       │◄──►│   Presenter     │◄──►│     Model       │
│  (Frontend UI)  │    │ (Business Logic)│    │ (Data & Domain) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
│                      │                      │
│ Current: Flask       │ Current: Flask       │ Backend Services
│ Future: Vue.js       │ Future: FastAPI      │ (Shared)
└─────────────────────┘└─────────────────────┘└─────────────────┘
```

### System Integration

The PTS Renamer integrates with existing system components using **unified database architecture**:

```
┌──────────────────────────────────────────────────────────────┐
│                    Existing System                           │
├──────────────────────────────────────────────────────────────┤
│  backend/shared/                                             │
│  ├── application/     # Shared use cases                     │
│  ├── domain/          # Core business entities               │
│  ├── infrastructure/  # Common adapters + UNIFIED DB         │
│  └── utils/           # Shared utilities                     │
└──────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌──────────────────────────────────────────────────────────────┐
│                PTS Renamer Module                            │
├──────────────────────────────────────────────────────────────┤
│  backend/pts_renamer/                                        │
│  ├── models/          # pts_rename_*.py (Data Models)        │
│  ├── services/        # pts_rename_*.py (Business Logic)     │
│  ├── repositories/    # pts_rename_*.py (Unified DB Access)   │
│  └── api/             # pts_rename_*.py (API Endpoints)      │
│                                                              │
│  frontend/pts_renamer/                                       │
│  ├── routes/          # pts_rename_*.py (Flask Routes)       │
│  ├── templates/       # pts_rename_*.html (HTML Templates)   │
│  ├── static/          # pts_rename_*.js/css (Assets)         │
│  └── components/      # pts_rename_*.html (Reusable UI)      │
└──────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌──────────────────────────────────────────────────────────────┐
│         Unified Database (outlook.db) + Dramatiq            │
├──────────────────────────────────────────────────────────────┤
│  • Single DB: outlook.db (email + pts_rename_ tables)      │
│  • Unified monitoring & backup     • Shared connection pool   │
│  • Existing Dramatiq tasks        • Simplified maintenance    │
└──────────────────────────────────────────────────────────────┘
```

### Unified Database Architecture Benefits

**✅ Operational Excellence:**
- **Single Point of Management**: All data in `outlook.db` simplifies administration
- **Unified Monitoring**: One database health dashboard for all components
- **Streamlined Backups**: Single backup process covers all application data
- **Reduced Configuration**: No additional database setup or connection management

**✅ Development Efficiency:**
- **Consistent Patterns**: Same database access patterns across all modules
- **Shared Infrastructure**: Reuse existing connection pools and query optimizations
- **Simplified Testing**: Single database for integration and E2E tests
- **Easier Troubleshooting**: All data accessible from one location

## Components and Interfaces

### Model Layer (Data & Domain)

#### Core Data Models

**pts_rename_models.py**
```python
from pydantic import BaseModel
from typing import List, Optional, Dict
from enum import Enum

class PTSRenameOperation(str, Enum):
    RENAME = "rename"
    QC_GENERATION = "qc_generation" 
    DIRECTORY_CREATION = "directory_creation"

class PTSRenameJobRequest(BaseModel):
    upload_id: str
    operations: List[PTSRenameOperation]
    rename_config: Optional[Dict[str, str]] = None
    qc_enabled: bool = False
    create_directories: bool = False

class PTSRenameJobStatus(BaseModel):
    job_id: str
    status: str  # pending, processing, compressing, completed, failed
    progress: int  # 0-100
    files_processed: int
    total_files: int
    error_message: Optional[str] = None
    result_download_url: Optional[str] = None
    compressed_file_size: Optional[int] = None
    compressed_file_name: Optional[str] = None
    download_expires_at: Optional[datetime] = None
```

**pts_rename_entities.py**
```python
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional

@dataclass
class PTSFile:
    original_path: Path
    filename: str
    size: int
    checksum: str
    
@dataclass  
class PTSRenameResult:
    original_name: str
    new_name: str
    operation: str
    success: bool
    error_message: Optional[str] = None

@dataclass
class PTSProcessingJob:
    job_id: str
    upload_id: str
    pts_files: List[PTSFile]
    operations: List[str]
    results: List[PTSRenameResult]
    status: str
```

#### Repository Layer

**pts_rename_repository.py**
```python
from abc import ABC, abstractmethod
from typing import List, Optional
from .pts_rename_entities import PTSProcessingJob, PTSFile

class IPTSRenameRepository(ABC):
    @abstractmethod
    async def save_job(self, job: PTSProcessingJob) -> str:
        pass
    
    @abstractmethod
    async def get_job(self, job_id: str) -> Optional[PTSProcessingJob]:
        pass
    
    @abstractmethod
    async def update_job_status(self, job_id: str, status: str) -> bool:
        pass
    
    @abstractmethod
    async def get_pts_files(self, upload_id: str) -> List[PTSFile]:
        pass

class PTSRenameSQLRepository(IPTSRenameRepository):
    """
    Repository implementation using unified outlook.db SQLite database
    
    Database path: outlook.db (unified database)
    Benefits:
    - Simplified database management (single database)
    - Unified monitoring and backup procedures
    - Reduced operational complexity
    - Consistent maintenance workflows
    - Better integration with existing database infrastructure
    """
    
    def __init__(self, database_path: str = "outlook.db"):
        self.database_path = database_path
        # Initialize connection to unified database
    
    # Implementation using unified database infrastructure
    pass
```

### Presenter Layer (Business Logic)

#### Core Services

**pts_rename_presenter.py**
```python
from typing import List, Dict, Any
from .pts_rename_models import PTSRenameJobRequest, PTSRenameJobStatus
from .pts_rename_services import PTSRenameService
from backend.shared.application.interfaces import ILogger

class PTSRenamePresenter:
    def __init__(self, 
                 pts_service: PTSRenameService,
                 logger: ILogger):
        self.pts_service = pts_service
        self.logger = logger
    
    async def handle_upload_request(self, files: List[Any]) -> Dict[str, Any]:
        """Handle file upload and return upload ID"""
        pass
    
    async def handle_processing_request(self, request: PTSRenameJobRequest) -> PTSRenameJobStatus:
        """Handle PTS processing request"""
        pass
    
    async def get_job_status(self, job_id: str) -> PTSRenameJobStatus:
        """Get processing job status"""
        pass
    
    async def get_preview(self, upload_id: str, operations: List[str]) -> Dict[str, Any]:
        """Generate processing preview"""
        pass
```

**pts_rename_service.py**
```python
from typing import List, Dict, Any
from .pts_rename_processor import PTSRenameProcessor
from .pts_rename_qc_generator import PTSQCGenerator
from .pts_rename_directory_manager import PTSDirectoryManager
from .pts_rename_download_service import PTSRenameDownloadService

class PTSRenameService:
    def __init__(self,
                 processor: PTSRenameProcessor,
                 qc_generator: PTSQCGenerator,
                 directory_manager: PTSDirectoryManager,
                 download_service: PTSRenameDownloadService):
        self.processor = processor
        self.qc_generator = qc_generator
        self.directory_manager = directory_manager
        self.download_service = download_service
    
    async def process_pts_files(self, job_request: PTSRenameJobRequest) -> str:
        """
        Process PTS files according to request
        Automatically compresses results and prepares for download
        """
        pass
    
    async def generate_preview(self, upload_id: str, operations: List[str]) -> Dict[str, Any]:
        """Generate processing preview"""
        pass
    
    async def finalize_processing(self, job_id: str, processed_files: List[str]) -> str:
        """
        Finalize processing by compressing results and creating download URL
        Returns download URL for frontend
        """
        pass
```

#### Specialized Processors

**pts_rename_processor.py**
```python
import re
from pathlib import Path
from typing import Dict, List, Tuple

class PTSRenameProcessor:
    def __init__(self):
        pass
    
    def rename_file(self, file_path: Path, old_pattern: str, new_pattern: str) -> Tuple[bool, str, str]:
        """
        Rename PTS file based on pattern
        Returns: (success, old_name, new_name)
        """
        pass
    
    def validate_rename_pattern(self, old_pattern: str, new_pattern: str) -> Tuple[bool, str]:
        """Validate rename patterns"""
        pass
    
    def preview_rename(self, files: List[Path], old_pattern: str, new_pattern: str) -> List[Dict]:
        """Preview rename operations"""
        pass
```

**pts_rename_qc_generator.py**
```python
from pathlib import Path
from typing import Tuple, List

class PTSQCGenerator:
    def create_qc_file(self, pts_file_path: Path) -> Tuple[bool, str]:
        """
        Create QC file from PTS file
        Removes data between "Parameter," and "QA," sections
        Returns: (success, qc_filename or error_message)
        """
        pass
    
    def modify_qc_content(self, qc_file_path: Path) -> bool:
        """Apply QC-specific modifications"""
        pass
    
    def preview_qc_generation(self, files: List[Path]) -> List[Dict]:
        """Preview QC file generation"""
        pass
```

**pts_rename_directory_manager.py**
```python
from pathlib import Path
from typing import Tuple, List, Dict

class PTSDirectoryManager:
    def create_pts_directory(self, pts_file: Path, original_folder: Path) -> Tuple[bool, str]:
        """
        Create directory structure for PTS file
        Returns: (success, directory_name or error_message)
        """
        pass
    
    def copy_folder_contents(self, source: Path, target: Path, pts_filename: str) -> bool:
        """Copy folder contents excluding other PTS files"""
        pass
    
    def preview_directory_creation(self, files: List[Path]) -> List[Dict]:
        """Preview directory creation operations"""
        pass

**pts_rename_download_service.py**
```python
from pathlib import Path
from typing import List, Tuple, Dict, Any
from backend.shared.infrastructure.adapters.compression import ICompressionService

class PTSRenameDownloadService:
    def __init__(self, compression_service: ICompressionService):
        self.compression_service = compression_service
    
    async def compress_processed_files(self, job_id: str, processed_files: List[Path]) -> Tuple[bool, str]:
        """
        Compress all processed files into downloadable archive
        Uses existing compression infrastructure
        Returns: (success, compressed_file_path or error_message)
        """
        pass
    
    async def create_download_package(self, job_id: str, results: Dict[str, Any]) -> str:
        """
        Create complete download package with:
        - Original files (if requested)
        - Renamed files
        - QC files (if generated)
        - Directory structures (if created)
        - Processing report/log
        Returns: compressed_file_path
        """
        pass
    
    def generate_processing_report(self, job_id: str, results: List[Dict]) -> str:
        """Generate processing report for inclusion in download package"""
        pass
    
    def get_download_url(self, job_id: str, compressed_file_path: str) -> str:
        """Generate secure download URL for frontend"""
        pass
    
    async def cleanup_processed_files(self, job_id: str, retention_hours: int = 24) -> bool:
        """Clean up processed files after retention period"""
        pass
```

#### Integration Services

**pts_rename_upload_service.py**
```python
from typing import List, Dict, Any
from backend.shared.infrastructure.adapters.file_storage import IFileStorage

class PTSRenameUploadService:
    def __init__(self, file_storage: IFileStorage):
        self.file_storage = file_storage
    
    async def handle_compressed_upload(self, files: List[Any]) -> str:
        """
        Handle compressed file upload
        Queue decompression using existing Dramatiq tasks
        Returns: upload_id
        """
        pass
    
    async def extract_pts_files(self, upload_id: str) -> List[Dict]:
        """Extract PTS files from uploaded archives"""
        pass
    
    async def validate_uploaded_files(self, files: List[Any]) -> Tuple[bool, str]:
        """Validate uploaded files"""
        pass
```

**pts_rename_dramatiq_integration.py**
```python
import dramatiq
from typing import Dict, Any, List

@dramatiq.actor(queue_name="pts_processing")
def pts_rename_process_job(job_id: str, job_data: Dict[str, Any]):
    """Dramatiq task for PTS processing"""
    pass

@dramatiq.actor(queue_name="pts_processing") 
def pts_rename_compress_results(job_id: str, result_files: List[str]):
    """
    Dramatiq task for compressing processed results using existing compression tasks
    Creates downloadable archive for frontend
    """
    pass

@dramatiq.actor(queue_name="pts_processing")
def pts_rename_finalize_job(job_id: str, compressed_file_path: str):
    """
    Finalize job and make compressed results available for download
    Updates job status and provides download URL to frontend
    """
    pass

class PTSRenameDramatiqService:
    def queue_processing_job(self, job_id: str, job_data: Dict[str, Any]) -> None:
        """Queue PTS processing job"""
        pass
    
    def queue_result_compression(self, job_id: str, files: List[str]) -> None:
        """Queue result compression using existing compression tasks"""
        pass
    
    def finalize_and_provide_download(self, job_id: str, compressed_file: str) -> str:
        """Finalize processing and return download URL for frontend"""
        pass
```

### View Layer (User Interface)

#### Flask Routes (Current Implementation)

**pts_rename_flask_routes.py**
```python
from flask import Blueprint, render_template, request, jsonify, send_file
from .pts_rename_presenter import PTSRenamePresenter

pts_renamer_bp = Blueprint('pts_renamer', __name__, url_prefix='/pts-renamer')

@pts_renamer_bp.route('/')
def index():
    """Main PTS Renamer interface at http://localhost:5000/pts-renamer/"""
    return render_template('pts_rename_main.html')

@pts_renamer_bp.route('/api/upload', methods=['POST'])
async def upload_files():
    """Handle file upload"""
    pass

@pts_renamer_bp.route('/api/process', methods=['POST'])
async def process_files():
    """Handle processing request - automatically compresses results"""
    pass

@pts_renamer_bp.route('/api/status/<job_id>')
async def get_job_status(job_id: str):
    """Get job status with download URL when completed"""
    pass

@pts_renamer_bp.route('/api/preview', methods=['POST'])
async def get_preview():
    """Get processing preview"""
    pass

@pts_renamer_bp.route('/api/download/<job_id>')
async def download_results(job_id: str):
    """Download compressed processing results"""
    pass

@pts_renamer_bp.route('/api/download/<job_id>/direct')
async def download_results_direct(job_id: str):
    """Direct download of compressed results file"""
    pass
```

#### FastAPI Endpoints (Future Implementation)

**pts_rename_fastapi_routes.py**
```python
from fastapi import APIRouter, UploadFile, File
from fastapi.responses import FileResponse
from typing import List
from .pts_rename_models import PTSRenameJobRequest, PTSRenameJobStatus

router = APIRouter(prefix="/api/v1/pts-renamer", tags=["PTS Renamer"])

@router.post("/upload")
async def upload_files(files: List[UploadFile] = File(...)):
    """Handle file upload"""
    pass

@router.post("/process", response_model=PTSRenameJobStatus)
async def process_files(request: PTSRenameJobRequest):
    """Handle processing request - automatically compresses results"""
    pass

@router.get("/status/{job_id}", response_model=PTSRenameJobStatus)
async def get_job_status(job_id: str):
    """Get job status with download URL when completed"""
    pass

@router.post("/preview")
async def get_preview(upload_id: str, operations: List[str]):
    """Get processing preview"""
    pass

@router.get("/download/{job_id}")
async def download_results(job_id: str) -> FileResponse:
    """Download compressed processing results"""
    pass

@router.get("/download/{job_id}/info")
async def get_download_info(job_id: str):
    """Get download information (file size, name, etc.)"""
    pass
```

#### Templates and Static Assets

**pts_rename_main.html**
- Modern web interface with drag-and-drop upload
- Real-time progress indicators
- Processing options (rename, QC, directories)
- Preview functionality
- Download results

**pts_rename_upload.js**
- File upload handling
- Progress tracking
- Real-time status updates

**pts_rename_processor.js**
- Processing configuration
- Preview generation
- Result display

## Data Models

### Database Schema (統一 outlook.db)

**使用統一的 `outlook.db` SQLite 資料庫**，採用表格前綴分離實現邏輯獨立：

```sql
-- PTS Processing Jobs (outlook.db)
CREATE TABLE pts_rename_jobs (
    id TEXT PRIMARY KEY,  -- 使用 TEXT 而非 UUID (SQLite 兼容)
    upload_id TEXT NOT NULL,
    status TEXT NOT NULL,
    operations TEXT NOT NULL,  -- JSON 字串格式
    progress INTEGER DEFAULT 0,
    files_processed INTEGER DEFAULT 0,
    total_files INTEGER DEFAULT 0,
    error_message TEXT,
    result_download_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- PTS Files (outlook.db)
CREATE TABLE pts_rename_files (
    id TEXT PRIMARY KEY,
    job_id TEXT REFERENCES pts_rename_jobs(id),
    original_path TEXT NOT NULL,
    filename TEXT NOT NULL,
    size INTEGER NOT NULL,
    checksum TEXT NOT NULL,
    processed INTEGER DEFAULT 0,  -- SQLite 使用 INTEGER 作為 BOOLEAN
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Processing Results (outlook.db)
CREATE TABLE pts_rename_results (
    id TEXT PRIMARY KEY,
    job_id TEXT REFERENCES pts_rename_jobs(id),
    file_id TEXT REFERENCES pts_rename_files(id),
    operation TEXT NOT NULL,
    original_name TEXT NOT NULL,
    new_name TEXT,
    success INTEGER NOT NULL,  -- SQLite BOOLEAN
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引優化
CREATE INDEX idx_pts_jobs_status ON pts_rename_jobs(status);
CREATE INDEX idx_pts_jobs_created ON pts_rename_jobs(created_at);
CREATE INDEX idx_pts_files_job ON pts_rename_files(job_id);
CREATE INDEX idx_pts_results_job ON pts_rename_results(job_id);
```

**統一資料庫架構優勢：**

**✅ 運營簡化**:
- **單一資料庫管理**: 只需要管理 `outlook.db`
- **統一監控**: 所有資料在一個位置，監控系統更簡單
- **簡化備份**: 單一備份流程涵蓋所有資料
- **減少配置**: 不需要分離的 PTS 資料庫配置

**✅ 架構一致性**:
- **一致的方法**: 所有模組使用相同的資料庫策略
- **更容易維護**: 資料庫管理任務簡化
- **更好的可觀察性**: 資料庫健康監控的單一點
- **統一安全性**: 單一資料庫安全配置

**✅ 效能和可靠性**:
- **減少 I/O**: 不需要跨資料庫操作
- **更好的事務支援**: 跨所有表格的 ACID 屬性
- **簡化連接池**: 單一連接池管理
- **一致的備份/恢復**: 統一恢復流程中的所有資料

**資料庫檔案路徑**: `outlook.db`

**架構決策說明：**
回到統一 `outlook.db` 的架構決策基於以下運營優勢：
1. **運營簡化**: 單一資料庫降低管理複雜度
2. **監控統一**: 統一監控點，提升系統可觀察性
3. **備份策略**: 統一備份流程，降低資料遺失風險
4. **維護效率**: 簡化資料庫維護和故障排除流程
5. **邏輯獨立**: 使用 `pts_rename_` 前綴保持功能獨立性

## Error Handling

### Error Categories

1. **Upload Errors**
   - Invalid file format
   - File size limits
   - Malicious content detection

2. **Processing Errors**
   - Invalid rename patterns
   - File access permissions
   - Disk space limitations

3. **System Errors**
   - Dramatiq task failures
   - Database connection issues
   - Network timeouts

### Error Response Format

```python
class PTSRenameError(BaseModel):
    error_code: str
    error_message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime
    job_id: Optional[str] = None
```

## Testing Strategy

### Unit Tests
- `test_pts_rename_processor.py` - File renaming logic
- `test_pts_rename_qc_generator.py` - QC file generation
- `test_pts_rename_directory_manager.py` - Directory creation
- `test_pts_rename_presenter.py` - Business logic

### Integration Tests
- `test_pts_rename_api_integration.py` - API endpoint testing
- `test_pts_rename_dramatiq_integration.py` - Task queue integration
- `test_pts_rename_file_processing.py` - End-to-end file processing

### E2E Tests
- `test_pts_rename_web_interface.py` - Web interface testing using Playwright
- `test_pts_rename_upload_flow.py` - Complete upload and processing flow

## Deployment Considerations

### Database Management

#### Unified Database Integration
**Database Path**: `outlook.db`

**Initial Setup**:
```bash
# PTS tables are created in existing outlook.db
# No separate database initialization required

# Initialize PTS schema in existing database
python scripts/init_pts_schema.py

# Verify PTS tables in unified database
python scripts/validate_pts_schema.py
```

#### Monitoring Integration Simplification
**ADVANTAGE**: Unified database simplifies monitoring:

1. **Database Health Checks**:
   - PTS tables monitored through existing `outlook.db` monitoring
   - No additional database connection monitoring needed
   - Unified database size and growth monitoring

2. **Dashboard Updates**:
   - PTS job status integrated into existing database dashboard
   - PTS processing metrics use existing database monitoring infrastructure
   - Single database health dashboard for all components

3. **Alert Configuration**:
   - PTS functionality covered by existing database alerts
   - No additional database-specific monitoring needed
   - Simplified alert configuration and maintenance

#### Backup Strategy Simplification
**Unified Backup Advantages**:
```bash
# No additional backup scripts needed
# PTS data included in existing outlook.db backup

# Existing backup script already covers PTS tables
#!/bin/bash
DB_PATH="outlook.db"
BACKUP_DIR="backups/outlook"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create backup (includes all PTS data)
sqlite3 $DB_PATH ".backup $BACKUP_DIR/outlook_$TIMESTAMP.db"

# All PTS data automatically included
```

**Integration Benefits**:
- No changes needed to existing backup scripts
- PTS data automatically included in regular backups
- Simplified backup verification (single database check)
- Unified retention policies for all data

#### Configuration Management
```python
# Simplified configuration using existing database
PTS_RENAME_CONFIG = {
    "database": {
        "path": "outlook.db",  # Use existing unified database
        "table_prefix": "pts_rename_",  # Logical separation through naming
        "connection_timeout": 30,
        "max_connections": 5,  # Shared with existing email processing
        "backup_interval_hours": 6  # Uses existing backup schedule
    },
    "monitoring": {
        "health_check_interval": 60,  # Integrated with existing monitoring
        "metrics_retention_days": 30,
        "alert_thresholds": {
            "processing_failure_rate": 0.05,
            "database_size_mb": 1000,  # Shared database threshold
            "job_queue_length": 100
        }
    }
}
```

### Migration Considerations

#### Schema Migration (if needed)
If PTS tables don't exist in `outlook.db`:
```sql
-- Create PTS tables in existing unified database
-- No data migration needed - tables created in place

-- Verify PTS tables exist
SELECT name FROM sqlite_master 
WHERE type='table' AND name LIKE 'pts_rename_%';
```

#### Service Dependencies
**Simplified Service Startup**:
```python
# Service initialization uses existing database connection
def initialize_pts_rename_service():
    # Use existing unified database connection
    unified_db = get_unified_database_connection("outlook.db")
    
    # Verify PTS schema in unified database
    unified_db.validate_pts_schema()
    
    # No additional database registration needed
    # Uses existing monitoring for outlook.db
    
    return unified_db
```

## Migration Path to Vue.js + FastAPI

### Phase 1: Current Implementation (MVP)
- Flask routes with HTML templates
- MVP architecture established
- Integration with existing Dramatiq infrastructure
- **Unified database architecture with operational advantages**

### Phase 2: API Preparation
- FastAPI endpoints implemented alongside Flask routes
- API documentation and testing
- Frontend-backend decoupling
- **Simplified monitoring and backup using unified database**

### Phase 3: Vue.js Migration
- Vue.js components replace HTML templates
- API-only backend communication
- Modern SPA experience
- **Performance optimizations for unified database with table prefixes**

### Migration Benefits
- Clean separation of concerns through MVP
- Gradual migration without service interruption
- Reusable business logic across implementations
- Consistent data models and APIs
- **Simplified database management and unified scaling**