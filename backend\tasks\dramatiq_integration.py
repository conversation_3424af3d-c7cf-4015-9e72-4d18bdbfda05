"""
Dramatiq 任務整合模組

🎯 功能：
  - 將 Dramatiq 任務正確整合到主應用程式
  - 提供統一的任務註冊和管理接口
  - 純 Dramatiq 任務隊列系統

🔧 使用方式：
  from backend.tasks.dramatiq_integration import get_dramatiq_tasks
  tasks = get_dramatiq_tasks()
"""

import os
import sys
from typing import Dict, Any, Optional, List
from loguru import logger

# 添加項目根目錄到 Python 路徑
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class DramatiqTaskRegistry:
    """Dramatiq 任務註冊器"""
    
    def __init__(self):
        self.tasks = {}
        self.initialized = False
        
    def initialize(self):
        """初始化 Dramatiq 任務"""
        if self.initialized:
            return
            
        try:
            # 設置 Dramatiq 配置
            os.environ.setdefault('USE_MEMORY_BROKER', 'false')  # 預設使用 Redis
            
            # 導入 Dramatiq 配置
            import dramatiq_config
            
            # 導入任務定義
            import backend.tasks.services.dramatiq_tasks
            
            # 註冊任務
            self.tasks = {
                'process_complete_eqc_workflow': dramatiq_tasks.process_complete_eqc_workflow_task,
                'search_product': dramatiq_tasks.search_product_task,
                'run_csv_summary': dramatiq_tasks.run_csv_summary_task,
                'run_code_comparison': dramatiq_tasks.run_code_comparison_task,
                'health_check': dramatiq_tasks.health_check_task
            }
            
            # Add PTS tasks if available
            if hasattr(dramatiq_tasks, 'PTS_TASKS_AVAILABLE') and dramatiq_tasks.PTS_TASKS_AVAILABLE:
                self.tasks.update({
                    'process_pts_rename_job': dramatiq_tasks.process_pts_rename_job_task,
                    'pts_file_compression': dramatiq_tasks.pts_file_compression_task,
                    'pts_cleanup': dramatiq_tasks.pts_cleanup_task
                })
                logger.info("✅ PTS Renamer tasks registered in Dramatiq integration")
            
            self.initialized = True
            logger.info(f"✅ Dramatiq 任務註冊完成，共 {len(self.tasks)} 個任務")
            
        except Exception as e:
            logger.error(f"❌ Dramatiq 任務註冊失敗: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")
            raise
    
    def get_task(self, task_name: str):
        """獲取指定任務"""
        if not self.initialized:
            self.initialize()
        return self.tasks.get(task_name)
    
    def get_all_tasks(self) -> Dict[str, Any]:
        """獲取所有任務"""
        if not self.initialized:
            self.initialize()
        return self.tasks.copy()
    
    def is_available(self) -> bool:
        """檢查 Dramatiq 是否可用"""
        try:
            import dramatiq
            import redis
            
            # 測試 Redis 連接
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            
            return True
        except Exception as e:
            logger.warning(f"⚠️ Dramatiq 不可用: {e}")
            return False


# 全域任務註冊器實例
_task_registry = DramatiqTaskRegistry()


def get_dramatiq_tasks() -> Dict[str, Any]:
    """獲取 Dramatiq 任務字典"""
    return _task_registry.get_all_tasks()


def get_dramatiq_task(task_name: str):
    """獲取指定的 Dramatiq 任務"""
    return _task_registry.get_task(task_name)


def is_dramatiq_available() -> bool:
    """檢查 Dramatiq 是否可用"""
    return _task_registry.is_available()


def submit_eqc_workflow_task(folder_path: str, user_session_id: str = None, options: Optional[Dict[str, Any]] = None):
    """提交 EQC 工作流程任務"""
    task = get_dramatiq_task('process_complete_eqc_workflow')
    if task:
        return task.send(folder_path, user_session_id, options)
    else:
        raise RuntimeError("Dramatiq EQC 工作流程任務不可用")


def submit_product_search_task(product_name: str, search_paths: list = None, max_results: int = 1000, search_filters: dict = None):
    """提交產品搜索任務"""
    task = get_dramatiq_task('search_product')
    if task:
        return task.send(product_name, search_paths, max_results, search_filters)
    else:
        raise RuntimeError("Dramatiq 產品搜索任務不可用")


def submit_csv_summary_task(input_path: str):
    """提交 CSV 摘要任務"""
    task = get_dramatiq_task('run_csv_summary')
    if task:
        return task.send(input_path)
    else:
        raise RuntimeError("Dramatiq CSV 摘要任務不可用")


def submit_code_comparison_task(input_path: str):
    """提交代碼比較任務"""
    task = get_dramatiq_task('run_code_comparison')
    if task:
        return task.send(input_path)
    else:
        raise RuntimeError("Dramatiq 代碼比較任務不可用")


def submit_health_check_task():
    """提交健康檢查任務"""
    task = get_dramatiq_task('health_check')
    if task:
        return task.send()
    else:
        raise RuntimeError("Dramatiq 健康檢查任務不可用")


# ============================================================================
# 🔧 PTS Renamer Task Submission Functions
# ============================================================================

def submit_pts_rename_job_task(job_id: str, job_data: Dict[str, Any]):
    """提交 PTS 重命名任務"""
    task = get_dramatiq_task('process_pts_rename_job')
    if task:
        return task.send(job_id, job_data)
    else:
        raise RuntimeError("Dramatiq PTS 重命名任務不可用")


def submit_pts_compression_task(source_path: str, archive_name: str = None, job_id: str = None):
    """提交 PTS 壓縮任務"""
    task = get_dramatiq_task('pts_file_compression')
    if task:
        return task.send(source_path, archive_name, job_id)
    else:
        raise RuntimeError("Dramatiq PTS 壓縮任務不可用")


def submit_pts_cleanup_task(job_id: str, cleanup_paths: List[str] = None, retention_hours: int = 24):
    """提交 PTS 清理任務"""
    task = get_dramatiq_task('pts_cleanup')
    if task:
        return task.send(job_id, cleanup_paths, retention_hours)
    else:
        raise RuntimeError("Dramatiq PTS 清理任務不可用")


def health_check():
    """執行健康檢查"""
    task = get_dramatiq_task('health_check')
    if task:
        return task.send()
    else:
        raise RuntimeError("Dramatiq 健康檢查任務不可用")


# 任務狀態檢查功能
class DramatiqTaskStatus:
    """Dramatiq 任務狀態檢查器"""

    @staticmethod
    def check_task_status(task_id: str) -> Dict[str, Any]:
        """
        檢查 Dramatiq 任務狀態 (修復版 - 避免卡死)

        🔧 修復內容：
        - 移除危險的 Redis 隊列掃描
        - 只使用 Dramatiq 結果後端
        - 添加超時保護
        """
        try:
            from dramatiq_config import get_result_backend
            import asyncio

            # 獲取結果後端
            result_backend = get_result_backend()

            try:
                # 🔧 修復：使用 get_result 方法直接獲取結果
                result = result_backend.get_result(task_id, block=False)

                if result is not None:
                    # 檢查結果是否表示失敗
                    if isinstance(result, dict) and result.get('status') == 'failed':
                        return {
                            'status': 'failed',
                            'result': result,
                            'message': result.get('error', '任務執行失敗'),
                            'progress': 100,
                            'error_type': result.get('error_type', 'Unknown'),
                            'retryable': result.get('retryable', True)
                        }
                    else:
                        return {
                            'status': 'completed',
                            'result': result,
                            'message': '任務完成',
                            'progress': 100
                        }

            except Exception:
                # 🔧 修復：任務不在結果後端中，可能還在隊列中或從未被接收
                return {
                    'status': 'pending',
                    'message': '任務等待處理中或狀態未知',
                    'progress': 0
                }

        except Exception as e:
            logger.error(f"❌ 檢查任務狀態失敗: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'message': '無法檢查任務狀態',
                'progress': 0
            }

    @staticmethod
    def check_task_status_enhanced(task_id: str) -> Dict[str, Any]:
        """
        增強的任務狀態檢查 - 多重驗證機制

        檢查順序：
        1. 結果後端檢查（主要）
        2. Redis 隊列掃描（備用）
        3. 任務元數據驗證（補充）
        """
        try:
            logger.debug(f"🔍 增強狀態檢查開始: {task_id}")

            # 🔧 第一步：嘗試標準結果後端檢查
            standard_result = DramatiqTaskStatus.check_task_status(task_id)

            # 如果標準檢查返回明確的完成或失敗狀態，直接返回
            if standard_result.get('status') in ['completed', 'failed']:
                logger.debug(f"✅ 標準檢查獲得明確狀態: {standard_result.get('status')}")
                return standard_result

            # 🔧 第二步：Redis 隊列掃描檢查
            queue_status = DramatiqTaskStatus._check_redis_queues(task_id)
            if queue_status:
                logger.debug(f"📊 Redis 隊列檢查結果: {queue_status}")
                return queue_status

            # 🔧 第三步：任務元數據檢查
            metadata_status = DramatiqTaskStatus._check_task_metadata(task_id)
            if metadata_status:
                logger.debug(f"📋 元數據檢查結果: {metadata_status}")
                return metadata_status

            # 🔧 如果所有檢查都無結果，返回標準結果
            logger.warning(f"⚠️ 所有檢查方法都無法確定任務狀態: {task_id}")
            return standard_result

        except Exception as e:
            logger.error(f"❌ 增強狀態檢查失敗: {e}")
            # 降級到標準檢查
            return DramatiqTaskStatus.check_task_status(task_id)

    @staticmethod
    def _check_redis_queues(task_id: str) -> Optional[Dict[str, Any]]:
        """檢查 Redis 隊列中的任務狀態"""
        try:
            import redis
            r = redis.Redis(host="localhost", port=6379, db=0)

            # 檢查各個隊列
            queues = ['eqc_queue', 'processing_queue', 'health_queue', 'search_queue']

            for queue_name in queues:
                queue_key = f"dramatiq:{queue_name}"
                queue_length = r.llen(queue_key)

                if queue_length > 0:
                    # 掃描隊列中的任務
                    messages = r.lrange(queue_key, 0, -1)
                    for message_data in messages:
                        try:
                            import json
                            message = json.loads(message_data.decode('utf-8'))
                            if message.get('message_id') == task_id:
                                logger.debug(f"📋 在隊列 {queue_name} 中找到任務: {task_id}")
                                return {
                                    'status': 'pending',
                                    'message': f'任務在隊列 {queue_name} 中等待處理',
                                    'progress': 0,
                                    'queue': queue_name
                                }
                        except Exception:
                            continue

            return None

        except Exception as e:
            logger.warning(f"⚠️ Redis 隊列檢查失敗: {e}")
            return None

    @staticmethod
    def _check_task_metadata(task_id: str) -> Optional[Dict[str, Any]]:
        """檢查任務元數據"""
        try:
            import redis
            r = redis.Redis(host="localhost", port=6379, db=0)

            # 檢查任務相關的 Redis 鍵
            task_keys = [
                f"dramatiq:task:{task_id}",
                f"dramatiq:result:{task_id}",
                f"dramatiq:status:{task_id}",
                f"dramatiq:metadata:{task_id}"
            ]

            for key in task_keys:
                if r.exists(key):
                    try:
                        data = r.get(key)
                        if data:
                            logger.debug(f"📋 找到任務元數據: {key}")
                            return {
                                'status': 'processing',
                                'message': '任務元數據存在，可能正在處理中',
                                'progress': 0,
                                'metadata_key': key
                            }
                    except Exception:
                        continue

            return None

        except Exception as e:
            logger.warning(f"⚠️ 任務元數據檢查失敗: {e}")
            return None


def get_task_status(task_id: str) -> Dict[str, Any]:
    """獲取任務狀態 - 增強版本"""
    return DramatiqTaskStatus.check_task_status_enhanced(task_id)


# 導出所有公共接口
__all__ = [
    'get_dramatiq_tasks',
    'get_dramatiq_task', 
    'is_dramatiq_available',
    'submit_eqc_workflow_task',
    'submit_product_search_task',
    'submit_csv_summary_task',
    'submit_code_comparison_task',
    'submit_pts_rename_job_task',
    'submit_pts_compression_task',
    'submit_pts_cleanup_task',
    'health_check',
    'get_task_status',
    'DramatiqTaskRegistry',
    'DramatiqTaskStatus'
]
