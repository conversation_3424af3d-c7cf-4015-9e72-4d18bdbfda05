from typing import Any, Optional, Union

def load_lib(name: str, cdecl: str) -> Any : ...
def c_ulong(x: int ) -> Any : ...
def c_ulonglong(x: int ) -> Any : ...
def c_size_t(x: int) -> Any : ...
def create_string_buffer(init_or_size: Union[bytes,int], size: Optional[int]) -> Any : ...
def get_c_string(c_string: Any) -> bytes : ...
def get_raw_buffer(buf: Any) -> bytes : ...
def c_uint8_ptr(data: Union[bytes, memoryview, bytearray]) -> Any : ...

class VoidPointer(object):
    def get(self) -> Any : ...
    def address_of(self) -> Any : ...

class SmartPointer(object):
    def __init__(self, raw_pointer: Any, destructor: Any) -> None : ...
    def get(self) -> Any : ...
    def release(self) -> Any : ...

backend : str
null_pointer : Any
ffi: Any

def load_pycryptodome_raw_lib(name: str, cdecl: str) -> Any : ...
def is_buffer(x: Any) -> bool : ...
def is_writeable_buffer(x: Any) -> bool : ...
