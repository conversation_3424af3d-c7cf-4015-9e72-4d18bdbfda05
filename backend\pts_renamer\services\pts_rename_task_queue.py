"""
PTS Renamer Task Queue Adapter

This module provides a TaskQueue implementation specifically for PTS renaming operations
that integrates with the existing Dramatiq infrastructure.
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import uuid

from loguru import logger

# Import Dramatiq tasks
from .pts_rename_dramatiq_integration import (
    process_pts_rename_job_task,
    pts_file_compression_task,
    pts_cleanup_task
)

# Import models
from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSJobStatus
)
from ..models.pts_rename_entities import PTSProcessingJob


class PTSRenameTaskQueue:
    """
    Task queue implementation for PTS renaming operations
    
    This adapter provides a clean interface for the service layer
    while integrating with the existing Dramatiq infrastructure.
    """
    
    def __init__(self):
        """Initialize PTS rename task queue"""
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
    
    async def enqueue_task(self, task_name: str, **kwargs) -> str:
        """
        Enqueue a PTS processing task
        
        Args:
            task_name: Name of the task to enqueue
            **kwargs: Task arguments
            
        Returns:
            Task ID for tracking
            
        Raises:
            ValueError: If task_name is not supported
        """
        try:
            task_id = f"pts_task_{uuid.uuid4().hex[:12]}"
            
            logger.info(f"[PTS-QUEUE] Enqueueing task: {task_name} with ID: {task_id}")
            
            # Route to appropriate Dramatiq task
            if task_name == "pts_rename_process_job":
                return await self._enqueue_processing_job(task_id, **kwargs)
            elif task_name == "pts_file_compression":
                return await self._enqueue_compression_job(task_id, **kwargs)
            elif task_name == "pts_cleanup":
                return await self._enqueue_cleanup_job(task_id, **kwargs)
            else:
                raise ValueError(f"Unsupported task: {task_name}")
                
        except Exception as e:
            logger.error(f"[PTS-QUEUE] Failed to enqueue task {task_name}: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task status information or None if not found
        """
        try:
            # Check active tasks first
            if task_id in self.active_tasks:
                return self.active_tasks[task_id]
            
            # For Dramatiq tasks, we'd need to check the result backend
            # For now, return basic status
            return {
                'task_id': task_id,
                'status': 'unknown',
                'message': 'Task status not available'
            }
            
        except Exception as e:
            logger.error(f"[PTS-QUEUE] Failed to get task status {task_id}: {e}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = 'cancelled'
                self.active_tasks[task_id]['cancelled_at'] = datetime.now().isoformat()
                logger.info(f"[PTS-QUEUE] Task {task_id} marked as cancelled")
                return True
            
            logger.warning(f"[PTS-QUEUE] Task {task_id} not found for cancellation")
            return False
            
        except Exception as e:
            logger.error(f"[PTS-QUEUE] Failed to cancel task {task_id}: {e}")
            return False
    
    async def cleanup_completed_tasks(self, retention_hours: int = 24) -> int:
        """
        Clean up completed tasks older than retention period
        
        Args:
            retention_hours: Hours after which tasks are cleaned up
            
        Returns:
            Number of tasks cleaned up
        """
        try:
            current_time = datetime.now()
            cleaned_count = 0
            
            for task_id, task_info in list(self.active_tasks.items()):
                if task_info.get('status') in ['completed', 'failed', 'cancelled']:
                    completed_at = task_info.get('completed_at') or task_info.get('created_at')
                    if completed_at:
                        task_time = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
                        age_hours = (current_time - task_time).total_seconds() / 3600
                        
                        if age_hours > retention_hours:
                            del self.active_tasks[task_id]
                            cleaned_count += 1
            
            logger.info(f"[PTS-QUEUE] Cleaned up {cleaned_count} completed tasks")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"[PTS-QUEUE] Failed to cleanup tasks: {e}")
            return 0
    
    def get_queue_statistics(self) -> Dict[str, Any]:
        """
        Get queue statistics
        
        Returns:
            Dictionary containing queue statistics
        """
        try:
            stats = {
                'total_tasks': len(self.active_tasks),
                'pending_tasks': 0,
                'processing_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'cancelled_tasks': 0
            }
            
            for task_info in self.active_tasks.values():
                status = task_info.get('status', 'unknown')
                if status == 'pending':
                    stats['pending_tasks'] += 1
                elif status == 'processing':
                    stats['processing_tasks'] += 1
                elif status == 'completed':
                    stats['completed_tasks'] += 1
                elif status == 'failed':
                    stats['failed_tasks'] += 1
                elif status == 'cancelled':
                    stats['cancelled_tasks'] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"[PTS-QUEUE] Failed to get queue statistics: {e}")
            return {}
    
    # Private helper methods
    
    async def _enqueue_processing_job(self, task_id: str, **kwargs) -> str:
        """Enqueue a PTS processing job"""
        job_id = kwargs.get('job_id')
        job_data = kwargs.get('job_data')
        
        if not job_id or not job_data:
            raise ValueError("job_id and job_data required for processing job")
        
        # Track the task
        self.active_tasks[task_id] = {
            'task_id': task_id,
            'job_id': job_id,
            'task_type': 'pts_rename_process_job',
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'job_data': job_data
        }
        
        # Submit to Dramatiq
        try:
            # Send task to Dramatiq (returns message, not awaitable)
            message = process_pts_rename_job_task.send(job_id, job_data)
            dramatiq_task_id = message.message_id
            
            # Update tracking with Dramatiq task ID
            self.active_tasks[task_id]['dramatiq_task_id'] = dramatiq_task_id
            self.active_tasks[task_id]['status'] = 'submitted'
            
            logger.info(f"[PTS-QUEUE] Processing job {job_id} submitted to Dramatiq: {dramatiq_task_id}")
            return task_id
            
        except Exception as e:
            self.active_tasks[task_id]['status'] = 'failed'
            self.active_tasks[task_id]['error'] = str(e)
            raise
    
    async def _enqueue_compression_job(self, task_id: str, **kwargs) -> str:
        """Enqueue a compression job"""
        source_path = kwargs.get('source_path')
        archive_name = kwargs.get('archive_name')
        job_id = kwargs.get('job_id')
        
        if not source_path:
            raise ValueError("source_path required for compression job")
        
        # Track the task
        self.active_tasks[task_id] = {
            'task_id': task_id,
            'task_type': 'pts_file_compression',
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'source_path': source_path,
            'archive_name': archive_name,
            'job_id': job_id
        }
        
        # Submit to Dramatiq
        try:
            message = pts_file_compression_task.send(source_path, archive_name, job_id)
            dramatiq_task_id = message.message_id
            
            # Update tracking
            self.active_tasks[task_id]['dramatiq_task_id'] = dramatiq_task_id
            self.active_tasks[task_id]['status'] = 'submitted'
            
            logger.info(f"[PTS-QUEUE] Compression job submitted to Dramatiq: {dramatiq_task_id}")
            return task_id
            
        except Exception as e:
            self.active_tasks[task_id]['status'] = 'failed'
            self.active_tasks[task_id]['error'] = str(e)
            raise
    
    async def _enqueue_cleanup_job(self, task_id: str, **kwargs) -> str:
        """Enqueue a cleanup job"""
        job_id = kwargs.get('job_id')
        cleanup_paths = kwargs.get('cleanup_paths', [])
        retention_hours = kwargs.get('retention_hours', 24)
        
        if not job_id:
            raise ValueError("job_id required for cleanup job")
        
        # Track the task
        self.active_tasks[task_id] = {
            'task_id': task_id,
            'task_type': 'pts_cleanup',
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'job_id': job_id,
            'cleanup_paths': cleanup_paths,
            'retention_hours': retention_hours
        }
        
        # Submit to Dramatiq
        try:
            message = pts_cleanup_task.send(job_id, cleanup_paths, retention_hours)
            dramatiq_task_id = message.message_id
            
            # Update tracking
            self.active_tasks[task_id]['dramatiq_task_id'] = dramatiq_task_id
            self.active_tasks[task_id]['status'] = 'submitted'
            
            logger.info(f"[PTS-QUEUE] Cleanup job submitted to Dramatiq: {dramatiq_task_id}")
            return task_id
            
        except Exception as e:
            self.active_tasks[task_id]['status'] = 'failed'
            self.active_tasks[task_id]['error'] = str(e)
            raise


# Factory function for creating task queue instance
def create_pts_rename_task_queue() -> PTSRenameTaskQueue:
    """
    Factory function to create PTS Rename task queue instance
    
    Returns:
        Configured task queue instance
    """
    return PTSRenameTaskQueue()